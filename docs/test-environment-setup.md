# Setting Up a Supabase Test Environment

This guide explains how to set up a separate Supabase test environment for running automated tests.

## Why a Separate Test Environment?

Using a separate test environment for your tests has several advantages:

1. **Isolation**: Your test data won't interfere with your production data
2. **Safety**: Tests can freely create, modify, and delete data without affecting production
3. **Consistency**: Tests always start with a known state
4. **Performance**: Tests don't slow down your production database

## Step 1: Create a New Supabase Project

1. Log in to the [Supabase Dashboard](https://app.supabase.com)
2. Click "New Project"
3. Enter a name for your project (e.g., "GymSaaS Test")
4. Choose the same region as your production project for consistency
5. Set a secure database password
6. Click "Create new project"

## Step 2: Set Up the Database Schema

Once your project is created, you need to set up the database schema:

1. Go to the "SQL Editor" in your new project
2. Click "New Query"
3. Copy the contents of `supabase/test-schema.sql` into the query editor
4. Click "Run" to execute the SQL and create the tables and policies

Alternatively, you can use the setup script:

```bash
npm run test:setup
```

This script will:
- Clean up any existing test data
- Set up the database schema
- Create test users
- Insert test data

## Step 3: Configure Environment Variables

Update your `.env.test` file with the credentials for your test project:

```
# Supabase Test Environment Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-test-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-test-project-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-test-project-service-role-key

# App URL for testing
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Test configuration
TEST_MODE=true
NODE_ENV=test
```

You can find these values in your Supabase test project:

1. Go to Project Settings > API
2. Copy the URL, anon key, and service role key

## Step 4: Run Tests with the Test Environment

When running tests, make sure they use the test environment:

```bash
# Run unit tests with test environment
NODE_ENV=test npm run test:unit

# Run end-to-end tests with test environment
NODE_ENV=test npm test
```

Or use the combined test command:

```bash
npm run test:ci
```

This will:
1. Set up the test environment
2. Run unit tests
3. Run end-to-end tests

## Best Practices for Test Data

1. **Use fixed IDs**: Use predetermined UUIDs for test data to make tests predictable
2. **Clean up after tests**: Always clean up test data after tests run
3. **Use test-specific emails**: Use a pattern like `test-{timestamp}@example.com` for test users
4. **Isolate test runs**: Make sure concurrent test runs don't interfere with each other

## Troubleshooting

### Database Schema Issues

If you encounter errors related to the database schema:

1. Check the SQL logs in the Supabase dashboard
2. Make sure all tables and policies are created correctly
3. Try running the SQL manually in the SQL Editor

### Authentication Issues

If tests fail with authentication errors:

1. Check that your test environment variables are correct
2. Make sure test users are being created successfully
3. Check that RLS policies are set up correctly

### Test Data Issues

If tests fail due to missing or incorrect test data:

1. Run the setup script manually: `npm run test:setup`
2. Check the logs for any errors during data creation
3. Verify that test data exists in the Supabase dashboard
