/**
 * This script sets up the necessary SQL functions in the test database.
 */

const { createClient } = require("@supabase/supabase-js");
require("dotenv").config({ path: ".env.test" });

// Create a Supabase client with the service role key for the test environment
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// SQL to create the exec_sql function
const createExecSqlFunction = `
  CREATE OR REPLACE FUNCTION exec_sql(sql text)
  RETURNS void AS $$
  BEGIN
    EXECUTE sql;
  END;
  $$ LANGUAGE plpgsql SECURITY DEFINER;
`;

// SQL to enable the uuid-ossp extension
const enableUuidExtension = `
  CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
`;

// Main function to run the setup
async function main() {
  try {
    console.log("Setting up SQL functions in test database...");

    // Enable the uuid-ossp extension
    console.log("Enabling uuid-ossp extension...");
    let extensionError;
    try {
      const { error } = await supabase.rpc("exec_sql", {
        sql: enableUuidExtension,
      });
      extensionError = error;
    } catch (err) {
      // If exec_sql doesn't exist yet, we need to create it first using raw SQL
      extensionError = { message: "exec_sql function doesn't exist yet" };
    }

    if (extensionError) {
      console.log(
        "Could not enable extension using exec_sql, trying direct SQL..."
      );

      // Try to execute the SQL directly
      const { error: directExtensionError } = await supabase
        .from("_temp_")
        .select("*")
        .limit(1)
        .then(
          () => ({ error: null }),
          async () => {
            // Create a PostgreSQL function to execute the SQL
            const { error } = await supabase
              .from("pg_catalog.pg_proc")
              .select("*")
              .limit(1)
              .then(
                () => ({ error: null }),
                async () => {
                  // Try using the REST API to execute SQL
                  const response = await fetch(
                    `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/`,
                    {
                      method: "POST",
                      headers: {
                        "Content-Type": "application/json",
                        Authorization: `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
                        apikey: process.env.SUPABASE_SERVICE_ROLE_KEY,
                        Prefer: "params=single-object",
                      },
                      body: JSON.stringify({
                        query: enableUuidExtension,
                      }),
                    }
                  );

                  if (!response.ok) {
                    return { error: await response.json() };
                  }

                  return { error: null };
                }
              );

            return { error };
          }
        );

      if (directExtensionError) {
        console.error(
          "Could not enable uuid-ossp extension:",
          directExtensionError
        );
        console.log(
          "Please enable the uuid-ossp extension manually in the Supabase dashboard SQL editor."
        );
      } else {
        console.log("uuid-ossp extension enabled successfully.");
      }
    } else {
      console.log("uuid-ossp extension enabled successfully.");
    }

    // Create the exec_sql function
    console.log("Creating exec_sql function...");

    // Try to execute the SQL directly using the REST API
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/rpc/exec_sql`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
          apikey: process.env.SUPABASE_SERVICE_ROLE_KEY,
          Prefer: "params=single-object",
        },
        body: JSON.stringify({
          sql: createExecSqlFunction,
        }),
      }
    );

    if (!response.ok) {
      // If the function doesn't exist, create it using the SQL API
      const createFunctionResponse = await fetch(
        `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
            apikey: process.env.SUPABASE_SERVICE_ROLE_KEY,
            Prefer: "params=single-object",
          },
          body: JSON.stringify({
            query: createExecSqlFunction,
          }),
        }
      );

      if (!createFunctionResponse.ok) {
        console.error(
          "Could not create exec_sql function:",
          await createFunctionResponse.json()
        );
        console.log(
          "Please create the exec_sql function manually in the Supabase dashboard SQL editor."
        );
      } else {
        console.log("exec_sql function created successfully.");
      }
    } else {
      console.log("exec_sql function already exists.");
    }

    console.log("SQL functions setup complete!");
    process.exit(0);
  } catch (error) {
    console.error("Error setting up SQL functions:", error);
    process.exit(1);
  }
}

// Run the main function
main();
