/**
 * This script sets up the test database for running tests.
 * It creates test data in a separate Supabase test project.
 */

const { createClient } = require("@supabase/supabase-js");
const fs = require("fs");
const path = require("path");
require("dotenv").config({ path: ".env.test" });

// Create a Supabase client with the service role key for the test environment
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Test data
const testUsers = [
  {
    id: "00000000-0000-0000-0000-000000000001",
    email: "<EMAIL>",
    password: "password123",
  },
  {
    id: "00000000-0000-0000-0000-000000000002",
    email: "<EMAIL>",
    password: "password123",
  },
];

const testTenants = [
  {
    id: "00000000-0000-0000-0000-000000000001",
    name: "Test Gym",
    slug: "test-gym",
    owner_id: "00000000-0000-0000-0000-000000000001",
    created_at: new Date().toISOString(),
  },
  {
    id: "00000000-0000-0000-0000-000000000002",
    name: "Admin Gym",
    slug: "admin-gym",
    owner_id: "00000000-0000-0000-0000-000000000002",
    created_at: new Date().toISOString(),
  },
];

const testMembers = [
  {
    id: "00000000-0000-0000-0000-000000000001",
    tenant_id: "00000000-0000-0000-0000-000000000001",
    first_name: "John",
    last_name: "Doe",
    email: "<EMAIL>",
    status: "active",
    created_at: new Date().toISOString(),
  },
  {
    id: "00000000-0000-0000-0000-000000000002",
    tenant_id: "00000000-0000-0000-0000-000000000001",
    first_name: "Jane",
    last_name: "Smith",
    email: "<EMAIL>",
    status: "active",
    created_at: new Date().toISOString(),
  },
];

const testSubscriptionPlans = [
  {
    id: "00000000-0000-0000-0000-000000000001",
    tenant_id: "00000000-0000-0000-0000-000000000001",
    name: "Basic Plan",
    description: "Basic membership plan",
    price: 29.99,
    billing_cycle: "monthly",
    is_active: true,
    created_at: new Date().toISOString(),
  },
  {
    id: "00000000-0000-0000-0000-000000000002",
    tenant_id: "00000000-0000-0000-0000-000000000001",
    name: "Premium Plan",
    description: "Premium membership plan",
    price: 49.99,
    billing_cycle: "monthly",
    is_active: true,
    created_at: new Date().toISOString(),
  },
];

const testSubscriptions = [
  {
    id: "00000000-0000-0000-0000-000000000001",
    member_id: "00000000-0000-0000-0000-000000000001",
    plan_id: "00000000-0000-0000-0000-000000000001",
    start_date: new Date().toISOString(),
    status: "active",
    auto_renew: true,
    created_at: new Date().toISOString(),
  },
];

const testPayments = [
  {
    id: "00000000-0000-0000-0000-000000000001",
    subscription_id: "00000000-0000-0000-0000-000000000001",
    amount: 29.99,
    status: "succeeded",
    payment_date: new Date().toISOString(),
    payment_method: "credit_card",
    transaction_id: "txn_test_123",
    created_at: new Date().toISOString(),
  },
];

// Function to create test users
async function createTestUsers() {
  console.log("Creating test users...");

  for (const user of testUsers) {
    // Create user in auth.users
    const { error: authError } = await supabase.auth.admin.createUser({
      email: user.email,
      password: user.password,
      email_confirm: true,
      user_metadata: { is_test: true },
      id: user.id,
    });

    if (authError) {
      console.error(`Error creating test user ${user.email}:`, authError);
    } else {
      console.log(`Created test user: ${user.email}`);
    }
  }
}

// Function to set up the database schema
async function setupDatabaseSchema() {
  console.log("Setting up database schema...");

  try {
    // Read the SQL schema file
    const schemaPath = path.join(
      __dirname,
      "..",
      "supabase",
      "test-schema.sql"
    );
    const schema = fs.readFileSync(schemaPath, "utf8");

    // Execute the SQL schema
    const { error } = await supabase.rpc("exec_sql", { sql: schema });

    if (error) {
      console.error("Error setting up database schema:", error);
      return false;
    }

    console.log("Database schema set up successfully");
    return true;
  } catch (error) {
    console.error("Error reading or executing schema file:", error);
    return false;
  }
}

// Function to create test tables and data
async function setupTestTables() {
  console.log("Setting up test data...");

  // Create tenants table
  const { error: tenantsError } = await supabase
    .from("tenants")
    .insert(testTenants);

  if (tenantsError) {
    console.error("Error creating test tenants:", tenantsError);
  } else {
    console.log("Created test tenants");
  }

  // Create members table
  const { error: membersError } = await supabase
    .from("members")
    .insert(testMembers);

  if (membersError) {
    console.error("Error creating test members:", membersError);
  } else {
    console.log("Created test members");
  }

  // Create subscription plans table
  const { error: plansError } = await supabase
    .from("subscription_plans")
    .insert(testSubscriptionPlans);

  if (plansError) {
    console.error("Error creating test subscription plans:", plansError);
  } else {
    console.log("Created test subscription plans");
  }

  // Create subscriptions table
  const { error: subscriptionsError } = await supabase
    .from("subscriptions")
    .insert(testSubscriptions);

  if (subscriptionsError) {
    console.error("Error creating test subscriptions:", subscriptionsError);
  } else {
    console.log("Created test subscriptions");
  }

  // Create payments table
  const { error: paymentsError } = await supabase
    .from("payments")
    .insert(testPayments);

  if (paymentsError) {
    console.error("Error creating test payments:", paymentsError);
  } else {
    console.log("Created test payments");
  }
}

// Function to clean up test data
async function cleanupTestData() {
  console.log("Cleaning up existing test data...");

  // Delete test data from tables
  await supabase
    .from("payments")
    .delete()
    .neq("id", "00000000-0000-0000-0000-000000000000");
  await supabase
    .from("subscriptions")
    .delete()
    .neq("id", "00000000-0000-0000-0000-000000000000");
  await supabase
    .from("subscription_plans")
    .delete()
    .neq("id", "00000000-0000-0000-0000-000000000000");
  await supabase
    .from("members")
    .delete()
    .neq("id", "00000000-0000-0000-0000-000000000000");
  await supabase
    .from("tenants")
    .delete()
    .neq("id", "00000000-0000-0000-0000-000000000000");

  // Delete test users
  for (const user of testUsers) {
    const { error } = await supabase.auth.admin.deleteUser(user.id);
    if (error) {
      console.error(`Error deleting test user ${user.email}:`, error);
    }
  }

  console.log("Cleanup complete");
}

// Main function to run the setup
async function main() {
  try {
    // Clean up existing test data
    await cleanupTestData();

    // Set up the database schema
    const schemaSetup = await setupDatabaseSchema();

    if (!schemaSetup) {
      console.error("Failed to set up database schema. Aborting test setup.");
      process.exit(1);
    }

    // Create test users
    await createTestUsers();

    // Set up test tables and data
    await setupTestTables();

    console.log("Test database setup complete!");
  } catch (error) {
    console.error("Error setting up test database:", error);
    process.exit(1);
  } finally {
    process.exit(0);
  }
}

// Run the main function
main();
