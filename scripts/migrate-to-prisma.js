#!/usr/bin/env node

/**
 * Migration script to drop Supabase tables and recreate them using Prisma
 * This script will:
 * 1. Drop all existing tables in the public schema
 * 2. Run Prisma migrations to recreate the schema
 * 3. Optionally seed the database with initial data
 */

const { execSync } = require('child_process');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(query) {
  return new Promise(resolve => rl.question(query, resolve));
}

async function main() {
  console.log('🚀 Prisma Migration Script');
  console.log('This script will drop all tables and recreate them using Prisma.');
  console.log('');

  // Confirm the action
  const confirm = await question('⚠️  This will DELETE ALL DATA in your database. Are you sure? (yes/no): ');
  
  if (confirm.toLowerCase() !== 'yes') {
    console.log('Migration cancelled.');
    rl.close();
    return;
  }

  try {
    console.log('');
    console.log('📋 Step 1: Generating Prisma client...');
    execSync('pnpm db:generate', { stdio: 'inherit' });

    console.log('');
    console.log('🗑️  Step 2: Dropping existing tables...');
    
    // Create a temporary Prisma script to drop tables
    const dropTablesScript = `
const { PrismaClient } = require('@prisma/client');

async function dropTables() {
  const prisma = new PrismaClient();
  
  try {
    // Drop tables in the correct order (reverse of dependencies)
    await prisma.$executeRaw\`DROP TABLE IF EXISTS payments CASCADE\`;
    await prisma.$executeRaw\`DROP TABLE IF EXISTS subscriptions CASCADE\`;
    await prisma.$executeRaw\`DROP TABLE IF EXISTS subscription_plans CASCADE\`;
    await prisma.$executeRaw\`DROP TABLE IF EXISTS tenant_members CASCADE\`;
    await prisma.$executeRaw\`DROP TABLE IF EXISTS members CASCADE\`;
    await prisma.$executeRaw\`DROP TABLE IF EXISTS tenant_users CASCADE\`;
    await prisma.$executeRaw\`DROP TABLE IF EXISTS tenants CASCADE\`;
    
    console.log('✅ All tables dropped successfully');
  } catch (error) {
    console.error('Error dropping tables:', error);
  } finally {
    await prisma.$disconnect();
  }
}

dropTables();
`;

    // Write and execute the drop script
    require('fs').writeFileSync('temp-drop-tables.js', dropTablesScript);
    execSync('node temp-drop-tables.js', { stdio: 'inherit' });
    require('fs').unlinkSync('temp-drop-tables.js');

    console.log('');
    console.log('🔄 Step 3: Pushing Prisma schema to database...');
    execSync('pnpm db:push', { stdio: 'inherit' });

    console.log('');
    console.log('✅ Migration completed successfully!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Update your environment variables if needed');
    console.log('2. Test your application with the new Prisma setup');
    console.log('3. Consider running: pnpm db:studio to explore your database');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

main().catch(console.error);
