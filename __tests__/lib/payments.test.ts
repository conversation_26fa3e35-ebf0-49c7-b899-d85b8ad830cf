import { processPayment, createPayment } from "@/lib/payments";

// Mock the createServerActionClient function
jest.mock("@/lib/supabase/server", () => ({
  createServerActionClient: jest.fn().mockImplementation(() => ({
    from: jest.fn().mockReturnValue({
      insert: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      single: jest.fn().mockResolvedValue({
        data: {
          id: "payment-id",
          subscription_id: "subscription-id",
          amount: 100,
          status: "succeeded",
          payment_date: "2023-01-01T00:00:00Z",
          payment_method: "credit_card",
          transaction_id: "txn_123",
        },
        error: null,
      }),
    }),
  })),
}));

// Mock the setTimeout function
jest.useFakeTimers();

describe("Payments Module", () => {
  describe("createPayment", () => {
    it("should create a payment record", async () => {
      const paymentData = {
        subscription_id: "subscription-id",
        amount: 100,
        status: "succeeded",
        payment_date: "2023-01-01T00:00:00Z",
        payment_method: "credit_card",
        transaction_id: "txn_123",
      };

      const result = await createPayment(paymentData);

      expect(result).toEqual({
        id: "payment-id",
        subscription_id: "subscription-id",
        amount: 100,
        status: "succeeded",
        payment_date: "2023-01-01T00:00:00Z",
        payment_method: "credit_card",
        transaction_id: "txn_123",
      });
    });

    it("should throw an error if the database operation fails", async () => {
      // Create a new mock implementation for this test
      jest.mock("@/lib/supabase/server", () => ({
        createServerActionClient: jest.fn().mockImplementation(() => ({
          from: jest.fn().mockReturnValue({
            insert: jest.fn().mockReturnThis(),
            select: jest.fn().mockReturnThis(),
            single: jest.fn().mockResolvedValue({
              data: null,
              error: new Error("Database error"),
            }),
          }),
        })),
      }));

      // Re-import the function to use the new mock
      jest.resetModules();
      const { createPayment: createPaymentTest } = require("@/lib/payments");

      const paymentData = {
        subscription_id: "subscription-id",
        amount: 100,
        status: "succeeded",
        payment_date: "2023-01-01T00:00:00Z",
        payment_method: "credit_card",
      };

      await expect(createPaymentTest(paymentData)).rejects.toThrow(
        "Failed to create payment"
      );
    });
  });

  describe("processPayment", () => {
    // Mock Math.random to control the success/failure simulation
    const originalRandom = Math.random;

    beforeEach(() => {
      // Reset the mocks
      jest.clearAllMocks();
    });

    afterAll(() => {
      // Restore the original Math.random
      Math.random = originalRandom;
      jest.useRealTimers();
    });

    it("should process a successful payment", async () => {
      // Mock Math.random to always return 0.5 (success case)
      Math.random = jest.fn().mockReturnValue(0.5);

      const processPromise = processPayment(
        "subscription-id",
        100,
        "credit_card"
      );

      // Fast-forward the timer to simulate the delay
      jest.advanceTimersByTime(1000);

      const result = await processPromise;

      expect(result).toEqual({
        id: "payment-id",
        subscription_id: "subscription-id",
        amount: 100,
        status: "succeeded",
        payment_date: expect.any(String),
        payment_method: "credit_card",
        transaction_id: expect.stringContaining("txn_"),
      });
    });

    it("should process a failed payment", async () => {
      // Mock Math.random to always return 0.95 (failure case)
      Math.random = jest.fn().mockReturnValue(0.95);

      // Create a new mock implementation for this test
      jest.mock("@/lib/supabase/server", () => ({
        createServerActionClient: jest.fn().mockImplementation(() => ({
          from: jest.fn().mockReturnValue({
            insert: jest.fn().mockReturnThis(),
            select: jest.fn().mockReturnThis(),
            single: jest.fn().mockResolvedValue({
              data: {
                id: "payment-id",
                subscription_id: "subscription-id",
                amount: 100,
                status: "failed",
                payment_date: "2023-01-01T00:00:00Z",
                payment_method: "credit_card",
                transaction_id: null,
              },
              error: null,
            }),
          }),
        })),
      }));

      // Re-import the function to use the new mock
      jest.resetModules();
      const { processPayment: processPaymentTest } = require("@/lib/payments");

      const processPromise = processPaymentTest(
        "subscription-id",
        100,
        "credit_card"
      );

      // Fast-forward the timer to simulate the delay
      jest.advanceTimersByTime(1000);

      const result = await processPromise;

      expect(result).toEqual({
        id: "payment-id",
        subscription_id: "subscription-id",
        amount: 100,
        status: "failed",
        payment_date: expect.any(String),
        payment_method: "credit_card",
        transaction_id: null,
      });
    });
  });
});
