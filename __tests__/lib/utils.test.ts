import {
  formatCurrency,
  formatDate,
  generateSlug,
  getInitials,
  truncate,
  cn,
} from "@/lib/utils";

describe("Utility Functions", () => {
  describe("formatCurrency", () => {
    it("should format a number as USD currency", () => {
      expect(formatCurrency(1000)).toBe("$1,000.00");
      expect(formatCurrency(1234.56)).toBe("$1,234.56");
      expect(formatCurrency(0)).toBe("$0.00");
      expect(formatCurrency(-100)).toBe("-$100.00");
    });
  });

  describe("formatDate", () => {
    it("should format a date string", () => {
      const date = new Date("2023-01-15");
      expect(formatDate("2023-01-15")).toMatch(/Jan 15, 2023/);
      expect(formatDate(date)).toMatch(/Jan 15, 2023/);
    });
  });

  describe("generateSlug", () => {
    it("should convert a string to a slug format", () => {
      expect(generateSlug("Hello World")).toBe("hello-world");
      expect(generateSlug("Test String 123")).toBe("test-string-123");
      expect(generateSlug("Special @#$% Characters")).toBe(
        "special-characters"
      );
      expect(generateSlug("Multiple   Spaces")).toBe("multiple-spaces");
    });
  });

  describe("getInitials", () => {
    it("should return the initials of a name", () => {
      expect(getInitials("John Doe")).toBe("JD");
      expect(getInitials("Alice")).toBe("A");
      expect(getInitials("John Middle Doe")).toBe("JD");
    });
  });

  describe("truncate", () => {
    it("should truncate a string to the specified length", () => {
      expect(truncate("This is a long string", 10)).toBe("This is a ...");
      expect(truncate("Short", 10)).toBe("Short");
      expect(truncate("Exactly 10", 10)).toBe("Exactly 10");
    });
  });

  describe("cn", () => {
    it("should combine class names", () => {
      expect(cn("class1", "class2")).toBe("class1 class2");
      expect(cn("class1", { class2: true, class3: false })).toBe(
        "class1 class2"
      );
      expect(cn("class1", undefined, "class2")).toBe("class1 class2");
    });
  });
});
