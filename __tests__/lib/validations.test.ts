import {
  loginSchema,
  registerSchema,
  createTenantSchema,
  memberSchema,
  subscriptionPlanSchema,
  subscriptionSchema,
  paymentSchema
} from '@/lib/validations';

describe('Validation Schemas', () => {
  describe('loginSchema', () => {
    it('should validate valid login data', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'password123'
      };
      
      const result = loginSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });
    
    it('should reject invalid email', () => {
      const invalidData = {
        email: 'not-an-email',
        password: 'password123'
      };
      
      const result = loginSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.errors[0].path).toContain('email');
      }
    });
    
    it('should reject short password', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: '12345'
      };
      
      const result = loginSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.errors[0].path).toContain('password');
      }
    });
  });
  
  describe('registerSchema', () => {
    it('should validate valid registration data', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123'
      };
      
      const result = registerSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });
    
    it('should reject mismatched passwords', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'different'
      };
      
      const result = registerSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.errors[0].path).toContain('confirmPassword');
      }
    });
  });
  
  describe('createTenantSchema', () => {
    it('should validate valid tenant data', () => {
      const validData = {
        name: 'My Gym',
        slug: 'my-gym'
      };
      
      const result = createTenantSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });
    
    it('should reject invalid slug format', () => {
      const invalidData = {
        name: 'My Gym',
        slug: 'Invalid Slug!'
      };
      
      const result = createTenantSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.errors[0].path).toContain('slug');
      }
    });
  });
  
  describe('memberSchema', () => {
    it('should validate valid member data', () => {
      const validData = {
        first_name: 'John',
        last_name: 'Doe',
        email: '<EMAIL>',
        phone: '************',
        status: 'active',
        notes: 'Some notes'
      };
      
      const result = memberSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });
    
    it('should validate with optional fields missing', () => {
      const validData = {
        first_name: 'John',
        last_name: 'Doe',
        email: '<EMAIL>',
        status: 'active'
      };
      
      const result = memberSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });
    
    it('should reject invalid status', () => {
      const invalidData = {
        first_name: 'John',
        last_name: 'Doe',
        email: '<EMAIL>',
        status: 'invalid-status'
      };
      
      const result = memberSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.errors[0].path).toContain('status');
      }
    });
  });
  
  describe('subscriptionPlanSchema', () => {
    it('should validate valid subscription plan data', () => {
      const validData = {
        name: 'Premium Plan',
        description: 'Our best plan',
        price: 99.99,
        billing_cycle: 'monthly',
        features: ['Feature 1', 'Feature 2'],
        is_active: true
      };
      
      const result = subscriptionPlanSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });
    
    it('should coerce string price to number', () => {
      const validData = {
        name: 'Premium Plan',
        description: 'Our best plan',
        price: '99.99',
        billing_cycle: 'monthly',
        is_active: true
      };
      
      const result = subscriptionPlanSchema.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(typeof result.data.price).toBe('number');
        expect(result.data.price).toBe(99.99);
      }
    });
  });
});
