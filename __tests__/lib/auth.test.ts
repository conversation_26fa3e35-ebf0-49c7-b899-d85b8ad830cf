import { getCurrentUser, requireAuth, getUserTenants } from '@/lib/auth';
import { redirect } from 'next/navigation';

// Mock the createClient function
jest.mock('@/utils/supabase/server', () => ({
  createClient: jest.fn().mockImplementation(() => ({
    auth: {
      getUser: jest.fn().mockImplementation(() => {
        return {
          data: {
            user: { id: 'test-user-id', email: '<EMAIL>' }
          },
          error: null
        };
      })
    }
  }))
}));

// Mock the tenants module
jest.mock('@/lib/supabase/tenants', () => ({
  getUserTenants: jest.fn().mockImplementation(() => {
    return [
      { tenant_id: 'tenant-1', name: 'Tenant 1', role: 'admin' },
      { tenant_id: 'tenant-2', name: 'Tenant 2', role: 'member' }
    ];
  })
}));

describe('Auth Functions', () => {
  describe('getCurrentUser', () => {
    it('should return the current user when authenticated', async () => {
      const user = await getCurrentUser();
      expect(user).toEqual({ id: 'test-user-id', email: '<EMAIL>' });
    });

    it('should return null when there is an error', async () => {
      // Override the mock for this test
      const createClientMock = require('@/utils/supabase/server').createClient;
      createClientMock.mockImplementationOnce(() => ({
        auth: {
          getUser: jest.fn().mockImplementation(() => {
            return {
              data: { user: null },
              error: new Error('Auth error')
            };
          })
        }
      }));

      const user = await getCurrentUser();
      expect(user).toBeNull();
    });
  });

  describe('requireAuth', () => {
    it('should return the user when authenticated', async () => {
      const user = await requireAuth();
      expect(user).toEqual({ id: 'test-user-id', email: '<EMAIL>' });
    });

    it('should redirect to login when not authenticated', async () => {
      // Override the mock for this test
      const createClientMock = require('@/utils/supabase/server').createClient;
      createClientMock.mockImplementationOnce(() => ({
        auth: {
          getUser: jest.fn().mockImplementation(() => {
            return {
              data: { user: null },
              error: null
            };
          })
        }
      }));

      await requireAuth();
      expect(redirect).toHaveBeenCalledWith('/auth/login');
    });
  });

  describe('getUserTenants', () => {
    it('should return tenants for a user', async () => {
      const tenants = await getUserTenants('test-user-id');
      expect(tenants).toEqual([
        { tenant_id: 'tenant-1', name: 'Tenant 1', role: 'admin' },
        { tenant_id: 'tenant-2', name: 'Tenant 2', role: 'member' }
      ]);
    });
  });
});
