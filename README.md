# GymSaaS - Multi-tenant Membership Management

A modern, full-stack multi-tenant SaaS application for business users (like gyms, yoga studios, and clubs) to manage members and their subscriptions.

## Features

- **Multi-tenant Architecture**: Each business operates in its own isolated tenant context
- **Server-side Supabase Integration**: Secure authentication and database operations
- **Member Management**: Add, view, update, and delete members
- **Subscription Plans**: Create and manage subscription plans
- **Subscriptions**: Assign plans to members and track renewals
- **Mock Payment System**: Simulate payment processing
- **Dashboard**: View business statistics and metrics
- **Role-based Access Control**: Admin and Staff roles

## Tech Stack

- **Frontend**:

  - Next.js (App Router)
  - Tailwind CSS
  - ShadCN UI components
  - React Hook Form + Zod

- **Backend**:
  - Supabase (PostgreSQL)
  - Row Level Security (RLS) for tenant isolation
  - Server-side API routes and server actions

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- Supabase account

### Installation

1. Clone the repository:

   ```bash
   git clone https://github.com/yourusername/gym-saas.git
   cd gym-saas
   ```

2. Install dependencies:

   ```bash
   npm install
   ```

3. Set up environment variables:

   - Copy `.env.local.example` to `.env.local`
   - Fill in your Supabase credentials

4. Set up the Supabase database:

   - Create a new Supabase project
   - Run the SQL schema in `supabase/schema.sql`

5. Start the development server:

   ```bash
   npm run dev
   ```

6. Open [http://localhost:3000](http://localhost:3000) in your browser

## Project Structure

```
gym-saas/
├── public/              # Static assets
├── src/
│   ├── app/             # Next.js App Router
│   │   ├── api/         # API routes
│   │   ├── auth/        # Authentication pages
│   │   ├── dashboard/   # Dashboard pages
│   │   ├── tenant/      # Tenant-specific pages
│   │   └── page.tsx     # Landing page
│   ├── components/      # React components
│   │   ├── layout/      # Layout components
│   │   ├── providers/   # Context providers
│   │   └── ui/          # UI components (ShadCN)
│   ├── lib/             # Utility functions
│   │   ├── supabase/    # Supabase client
│   │   └── validations/ # Zod schemas
│   └── types/           # TypeScript type definitions
├── supabase/            # Supabase configuration
│   └── schema.sql       # Database schema
└── .env.local           # Environment variables
```

## Database Schema

- **tenants**: Business information
- **tenant_users**: User-tenant relationships and roles
- **members**: Member information
- **subscription_plans**: Subscription plan details
- **subscriptions**: Member-plan relationships
- **payments**: Payment transactions

## Security

- Server-side Supabase integration ensures no exposure of API keys in the browser
- Row Level Security (RLS) policies for tenant isolation
- Role-based access control for different permission levels

## Testing

The application includes both unit tests and end-to-end tests to ensure functionality works as expected.

### Test Environment

The application uses a separate Supabase test environment for testing to avoid affecting production data. See [Test Environment Setup](docs/test-environment-setup.md) for details on setting up a test environment.

### Unit Tests

Unit tests are implemented using Jest and focus on testing individual functions and components in isolation.

1. Run unit tests:

   ```bash
   npm run test:unit
   ```

2. Run unit tests with coverage:

   ```bash
   npm run test:unit -- --coverage
   ```

### Unit Test Structure

- `__tests__/` - Unit tests
  - `lib/` - Tests for utility functions and business logic
  - `components/` - Tests for React components

### End-to-End Tests

End-to-end tests are implemented using Playwright and test the application as a whole, simulating user interactions.

1. Install Playwright browsers (first time only):

   ```bash
   npm run test:install
   ```

2. Run the end-to-end tests:

   ```bash
   npm test
   ```

3. Run tests with UI:

   ```bash
   npm run test:ui
   ```

4. Run tests in headed mode (visible browser):

   ```bash
   npm run test:headed
   ```

5. Debug tests:

   ```bash
   npm run test:debug
   ```

### End-to-End Test Structure

- `e2e/` - End-to-end tests
  - `auth/` - Authentication tests (login, register)
  - `dashboard/` - Dashboard and business selection tests
  - `tenant/` - Tenant-specific tests
  - `fixtures/` - Test fixtures and utilities

### Setting Up Test Data

To set up the test database with test data:

```bash
npm run test:setup
```

### Running All Tests

To run all tests with the test environment:

```bash
npm run test:ci
```

This will:

1. Set up the test environment
2. Run unit tests
3. Run end-to-end tests
