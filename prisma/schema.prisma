// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
  schemas   = ["auth", "public"]
}

// Supabase auth.users table (read-only reference)
model User {
  id        String   @id @db.Uuid
  email     String?  @unique
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)

  // Relations
  ownedTenants Tenant[]     @relation("TenantOwner")
  tenantUsers  TenantUser[]
  members      Member[]     @relation("MemberAuth")

  @@map("users")
  @@schema("auth")
}

model Tenant {
  id                     String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt              DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  name                   String
  slug                   String    @unique
  logoUrl                String?   @map("logo_url")
  ownerId                String    @map("owner_id") @db.Uuid
  publicPageEnabled      Boolean   @default(false) @map("public_page_enabled")
  publicPageTitle        String?   @map("public_page_title")
  publicPageDescription  String?   @map("public_page_description")

  // Relations
  owner            User               @relation("TenantOwner", fields: [ownerId], references: [id], onDelete: Cascade)
  tenantUsers      TenantUser[]
  tenantMembers    TenantMember[]
  subscriptionPlans SubscriptionPlan[]

  @@map("tenants")
  @@schema("public")
}

model TenantUser {
  id        String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  tenantId  String   @map("tenant_id") @db.Uuid
  userId    String   @map("user_id") @db.Uuid
  role      String   // 'admin' | 'staff'

  // Relations
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([tenantId, userId])
  @@map("tenant_users")
  @@schema("public")
}

model Member {
  id          String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  firstName   String   @map("first_name")
  lastName    String   @map("last_name")
  email       String   @unique
  phone       String?  @unique
  authId      String?  @map("auth_id") @db.Uuid
  passwordSet Boolean  @default(false) @map("password_set")

  // Relations
  authUser       User?          @relation("MemberAuth", fields: [authId], references: [id], onDelete: SetNull)
  tenantMembers  TenantMember[]
  subscriptions  Subscription[]

  @@map("members")
  @@schema("public")
}

model TenantMember {
  id        String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  tenantId  String   @map("tenant_id") @db.Uuid
  memberId  String   @map("member_id") @db.Uuid
  status    String   // 'active' | 'inactive' | 'pending'
  notes     String?

  // Relations
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  member Member @relation(fields: [memberId], references: [id], onDelete: Cascade)

  @@unique([tenantId, memberId])
  @@map("tenant_members")
  @@schema("public")
}

model SubscriptionPlan {
  id           String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt    DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  tenantId     String   @map("tenant_id") @db.Uuid
  name         String
  description  String?
  price        Decimal  @db.Decimal(10, 2)
  billingCycle String   @map("billing_cycle") // 'monthly' | 'quarterly' | 'biannual' | 'annual'
  features     Json?    @db.JsonB
  isActive     Boolean  @default(true) @map("is_active")

  // Relations
  tenant        Tenant         @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  subscriptions Subscription[]

  @@map("subscription_plans")
  @@schema("public")
}

model Subscription {
  id        String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  memberId  String    @map("member_id") @db.Uuid
  planId    String    @map("plan_id") @db.Uuid
  startDate DateTime  @map("start_date") @db.Timestamptz(6)
  endDate   DateTime? @map("end_date") @db.Timestamptz(6)
  status    String    // 'active' | 'canceled' | 'expired' | 'pending'
  autoRenew Boolean   @default(true) @map("auto_renew")

  // Relations
  member   Member           @relation(fields: [memberId], references: [id], onDelete: Cascade)
  plan     SubscriptionPlan @relation(fields: [planId], references: [id], onDelete: Cascade)
  payments Payment[]

  @@map("subscriptions")
  @@schema("public")
}

model Payment {
  id             String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt      DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  subscriptionId String   @map("subscription_id") @db.Uuid
  amount         Decimal  @db.Decimal(10, 2)
  status         String   // 'succeeded' | 'failed' | 'pending' | 'refunded'
  paymentDate    DateTime @map("payment_date") @db.Timestamptz(6)
  paymentMethod  String   @map("payment_method") // 'credit_card' | 'debit_card' | 'bank_transfer' | 'cash'
  transactionId  String?  @map("transaction_id")

  // Relations
  subscription Subscription @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)

  @@map("payments")
  @@schema("public")
}
