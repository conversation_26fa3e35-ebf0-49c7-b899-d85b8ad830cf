// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// Supabase auth.users table (read-only reference)
model User {
  id        String   @id
  email     String?  @unique
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  // Relations
  ownedTenants Tenant[]     @relation("TenantOwner")
  tenantUsers  TenantUser[]
  members      Member[]     @relation("MemberAuth")

  @@map("users")
}

model Tenant {
  id                     String    @id @default(cuid())
  createdAt              DateTime  @default(now()) @map("created_at")
  name                   String
  slug                   String    @unique
  logoUrl                String?   @map("logo_url")
  ownerId                String    @map("owner_id")
  publicPageEnabled      Boolean   @default(false) @map("public_page_enabled")
  publicPageTitle        String?   @map("public_page_title")
  publicPageDescription  String?   @map("public_page_description")

  // Relations
  owner            User               @relation("TenantOwner", fields: [ownerId], references: [id], onDelete: Cascade)
  tenantUsers      TenantUser[]
  tenantMembers    TenantMember[]
  subscriptionPlans SubscriptionPlan[]

  @@map("tenants")
}

model TenantUser {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now()) @map("created_at")
  tenantId  String   @map("tenant_id")
  userId    String   @map("user_id")
  role      String   // 'admin' | 'staff'

  // Relations
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([tenantId, userId])
  @@map("tenant_users")
}

model Member {
  id          String   @id @default(cuid())
  createdAt   DateTime @default(now()) @map("created_at")
  firstName   String   @map("first_name")
  lastName    String   @map("last_name")
  email       String   @unique
  phone       String?  @unique
  authId      String?  @map("auth_id")
  passwordSet Boolean  @default(false) @map("password_set")

  // Relations
  authUser       User?          @relation("MemberAuth", fields: [authId], references: [id], onDelete: SetNull)
  tenantMembers  TenantMember[]
  subscriptions  Subscription[]

  @@map("members")
}

model TenantMember {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now()) @map("created_at")
  tenantId  String   @map("tenant_id")
  memberId  String   @map("member_id")
  status    String   // 'active' | 'inactive' | 'pending'
  notes     String?

  // Relations
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  member Member @relation(fields: [memberId], references: [id], onDelete: Cascade)

  @@unique([tenantId, memberId])
  @@map("tenant_members")
}

model SubscriptionPlan {
  id           String   @id @default(cuid())
  createdAt    DateTime @default(now()) @map("created_at")
  tenantId     String   @map("tenant_id")
  name         String
  description  String?
  price        Decimal  @db.Decimal(10, 2)
  billingCycle String   @map("billing_cycle") // 'monthly' | 'quarterly' | 'biannual' | 'annual'
  features     Json?    @db.JsonB
  isActive     Boolean  @default(true) @map("is_active")

  // Relations
  tenant        Tenant         @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  subscriptions Subscription[]

  @@map("subscription_plans")
}

model Subscription {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now()) @map("created_at")
  memberId  String    @map("member_id")
  planId    String    @map("plan_id")
  startDate DateTime  @map("start_date")
  endDate   DateTime? @map("end_date")
  status    String    // 'active' | 'canceled' | 'expired' | 'pending'
  autoRenew Boolean   @default(true) @map("auto_renew")

  // Relations
  member   Member           @relation(fields: [memberId], references: [id], onDelete: Cascade)
  plan     SubscriptionPlan @relation(fields: [planId], references: [id], onDelete: Cascade)
  payments Payment[]

  @@map("subscriptions")
}

model Payment {
  id             String   @id @default(cuid())
  createdAt      DateTime @default(now()) @map("created_at")
  subscriptionId String   @map("subscription_id")
  amount         Decimal  @db.Decimal(10, 2)
  status         String   // 'succeeded' | 'failed' | 'pending' | 'refunded'
  paymentDate    DateTime @map("payment_date")
  paymentMethod  String   @map("payment_method") // 'credit_card' | 'debit_card' | 'bank_transfer' | 'cash'
  transactionId  String?  @map("transaction_id")

  // Relations
  subscription Subscription @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)

  @@map("payments")
}
