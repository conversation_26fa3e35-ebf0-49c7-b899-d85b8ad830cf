import { test as authTest } from '../fixtures/auth';
import { expect } from '@playwright/test';
import { createTestTenant, cleanupTestTenant } from '../fixtures/utils';

authTest.describe('Business Selection', () => {
  let userId: string;
  let tenantId: string;

  authTest.beforeEach(async ({ createTestUser }) => {
    // Create a test user
    const testUser = await createTestUser();
    userId = testUser.userId;
  });

  authTest.afterEach(async ({ cleanupTestUser }) => {
    // Clean up the test tenant if it was created
    if (tenantId) {
      await cleanupTestTenant(tenantId);
    }
    
    // Clean up the test user
    await cleanupTestUser(userId);
  });

  authTest('should display the business selection page', async ({ page }) => {
    // Navigate to the business selection page
    await page.goto('/business/select');
    
    // Verify page components are visible
    await expect(page.getByRole('heading', { name: /select a business/i })).toBeVisible();
    await expect(page.getByText(/create new business/i)).toBeVisible();
  });

  authTest('should allow creating a new business', async ({ page }) => {
    // Navigate to the business selection page
    await page.goto('/business/select');
    
    // Click on create new business
    await page.click('text=Create New Business');
    
    // Fill in the business creation form
    const businessName = `Test Gym ${Date.now()}`;
    await page.fill('input[name="name"]', businessName);
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Wait for creation to complete and redirect
    await page.waitForURL('/tenant/**');
    
    // Verify we're on the tenant dashboard
    await expect(page.getByText(/dashboard/i)).toBeVisible();
    
    // Get the tenant ID from the URL for cleanup
    const url = page.url();
    const match = url.match(/\/tenant\/([^\/]+)/);
    if (match && match[1]) {
      // Find the tenant ID by slug
      const adminClient = createAdminClient();
      const { data } = await adminClient
        .from('tenants')
        .select('id')
        .eq('slug', match[1])
        .single();
      
      if (data) {
        tenantId = data.id;
      }
    }
  });

  authTest('should allow selecting an existing business', async ({ page }) => {
    // Create a test tenant for the user
    const tenant = await createTestTenant(userId);
    tenantId = tenant.id;
    
    // Navigate to the business selection page
    await page.goto('/business/select');
    
    // Wait for the tenant to appear in the list
    await page.waitForSelector(`text=${tenant.name}`);
    
    // Click on the tenant
    await page.click(`text=${tenant.name}`);
    
    // Wait for navigation to complete
    await page.waitForURL(`/tenant/${tenant.slug}`);
    
    // Verify we're on the tenant dashboard
    await expect(page.getByText(/dashboard/i)).toBeVisible();
  });
});
