import { test as authTest } from '../fixtures/auth';
import { expect } from '@playwright/test';
import { createTestTenant, cleanupTestTenant } from '../fixtures/utils';

authTest.describe('Tenant Dashboard', () => {
  let userId: string;
  let tenantId: string;

  authTest.beforeEach(async ({ page, createTestUser }) => {
    // Create a test user and tenant
    const testUser = await createTestUser();
    userId = testUser.userId;
    
    // Create a test tenant for the user
    const tenant = await createTestTenant(userId);
    tenantId = tenant.id;
    
    // Navigate to the tenant dashboard
    await page.goto(`/tenant/${tenant.slug}`);
  });

  authTest.afterEach(async ({ cleanupTestUser }) => {
    // Clean up the test tenant and user
    await cleanupTestTenant(tenantId);
    await cleanupTestUser(userId);
  });

  authTest('should display the tenant dashboard', async ({ page }) => {
    // Verify dashboard components are visible
    await expect(page.getByRole('heading', { name: /dashboard/i })).toBeVisible();
    await expect(page.getByText(/overview/i)).toBeVisible();
    
    // Sidebar navigation should be visible
    await expect(page.getByRole('navigation')).toBeVisible();
    await expect(page.getByText(/members/i)).toBeVisible();
    await expect(page.getByText(/subscriptions/i)).toBeVisible();
    await expect(page.getByText(/payments/i)).toBeVisible();
  });

  authTest('should navigate to members page', async ({ page }) => {
    // Click on the members link in the sidebar
    await page.click('text=Members');
    
    // Verify we're on the members page
    await expect(page).toHaveURL(/\/members/);
    await expect(page.getByRole('heading', { name: /members/i })).toBeVisible();
  });

  authTest('should navigate to subscriptions page', async ({ page }) => {
    // Click on the subscriptions link in the sidebar
    await page.click('text=Subscriptions');
    
    // Verify we're on the subscriptions page
    await expect(page).toHaveURL(/\/subscriptions/);
    await expect(page.getByRole('heading', { name: /subscriptions/i })).toBeVisible();
  });

  authTest('should navigate to payments page', async ({ page }) => {
    // Click on the payments link in the sidebar
    await page.click('text=Payments');
    
    // Verify we're on the payments page
    await expect(page).toHaveURL(/\/payments/);
    await expect(page.getByRole('heading', { name: /payments/i })).toBeVisible();
  });
});
