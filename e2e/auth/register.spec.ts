import { test, expect } from '@playwright/test';
import { createAdminClient, expectToast } from '../fixtures/utils';

test.describe('Registration Page', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/auth/register');
  });

  test('should display the registration form', async ({ page }) => {
    await expect(page.getByRole('heading', { name: /create an account/i })).toBeVisible();
    await expect(page.getByLabel(/email/i)).toBeVisible();
    await expect(page.getByLabel(/password/i)).toBeVisible();
    await expect(page.getByLabel(/business name/i)).toBeVisible();
    await expect(page.getByRole('button', { name: /register/i })).toBeVisible();
  });

  test('should show validation errors for invalid inputs', async ({ page }) => {
    // Submit empty form
    await page.click('button[type="submit"]');
    
    // Check for validation errors
    await expect(page.getByText(/email is required/i)).toBeVisible();
    await expect(page.getByText(/password is required/i)).toBeVisible();
    await expect(page.getByText(/business name is required/i)).toBeVisible();
  });

  test('should navigate to login page', async ({ page }) => {
    await page.click('text=Already have an account?');
    await expect(page).toHaveURL('/auth/login');
  });

  test('should register a new user and create a business', async ({ page }) => {
    // Generate unique test data
    const timestamp = Date.now();
    const testEmail = `test-${timestamp}@example.com`;
    const testPassword = 'Test@123456';
    const businessName = `Test Gym ${timestamp}`;
    
    // Fill in the registration form
    await page.fill('input[name="email"]', testEmail);
    await page.fill('input[name="password"]', testPassword);
    await page.fill('input[name="businessName"]', businessName);
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Wait for registration to complete and redirect
    await page.waitForURL('/tenant/**');
    
    // Verify we're on the tenant dashboard
    await expect(page.getByText(/dashboard/i)).toBeVisible();
    
    // Clean up: Delete the test user and tenant
    try {
      const adminClient = createAdminClient();
      
      // Find the user by email
      const { data: userData } = await adminClient.auth.admin.listUsers();
      const testUser = userData.users.find(user => user.email === testEmail);
      
      if (testUser) {
        // Delete the user
        await adminClient.auth.admin.deleteUser(testUser.id);
      }
    } catch (error) {
      console.error('Failed to clean up test user:', error);
    }
  });
});
