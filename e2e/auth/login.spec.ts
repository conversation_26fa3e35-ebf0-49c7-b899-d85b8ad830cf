import { test, expect } from "@playwright/test";
import { test as authTest } from "../fixtures/auth";
import { expectToast } from "../fixtures/utils";

test.describe("Login Page", () => {
  test.beforeEach(async ({ page }) => {
    await page.goto("/auth/login");
  });

  test("should display the login form", async ({ page }) => {
    // Take a screenshot to see what's on the page
    await page.screenshot({ path: "e2e/screenshots/login-form-test.png" });

    // Check for form elements without relying on specific heading
    await expect(page.locator('input[name="email"]')).toBeVisible();
    await expect(page.locator('input[name="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
  });

  test("should show error for invalid credentials", async ({ page }) => {
    console.log("Starting test for invalid credentials");

    // Fill in the form with invalid credentials
    await page.fill('input[name="email"]', "<EMAIL>");
    await page.fill('input[name="password"]', "wrongpassword");
    console.log("Filled in form with invalid credentials");

    // Take screenshot before submission
    await page.screenshot({
      path: "e2e/screenshots/invalid-credentials-before.png",
    });

    // Submit the form
    await page.click('button[type="submit"]');
    console.log("Submitted form with invalid credentials");

    // Take screenshot after submission
    await page.screenshot({
      path: "e2e/screenshots/invalid-credentials-after.png",
    });

    // Check for error toast or message
    try {
      await expectToast(page, /invalid|incorrect|wrong/i, 10000);
      console.log("Error toast displayed successfully");
    } catch {
      console.log("Could not find toast, checking for inline error message");

      // Check for inline error message
      const errorMessage = page.locator(
        '.text-destructive, .text-red-500, .text-error, [role="alert"]'
      );
      await expect(errorMessage).toBeVisible({ timeout: 5000 });
      console.log("Error message displayed successfully");
    }

    // Verify we're still on the login page
    await expect(page).toHaveURL("/auth/login");
    console.log("Still on login page as expected");
  });

  test("should navigate to register page", async ({ page }) => {
    // Take a screenshot to see what's on the page
    await page.screenshot({ path: "e2e/screenshots/register-link-test.png" });

    // Look for any link or button that might navigate to register
    const registerLink = page.getByRole("link", {
      name: /register|sign up|create.*account/i,
    });

    if ((await registerLink.count()) > 0) {
      await registerLink.click();
    } else {
      // Try alternative selectors if the link is not found
      const alternativeSelectors = [
        'a[href="/auth/register"]',
        'a:has-text("Create")',
        'a:has-text("Register")',
        'a:has-text("Sign up")',
      ];

      for (const selector of alternativeSelectors) {
        const link = page.locator(selector);
        if ((await link.count()) > 0) {
          console.log(`Found register link with selector: ${selector}`);
          await link.click();
          break;
        }
      }
    }

    // Wait for navigation and verify URL
    await expect(page).toHaveURL("/auth/register", { timeout: 5000 });
  });
});

// Test successful login using our auth fixtures
authTest(
  "should login successfully with valid credentials",
  async ({ page, createTestUser, cleanupTestUser }) => {
    // Create a test user
    console.log("Starting login test with valid credentials");
    const { email, password, userId } = await createTestUser();

    try {
      console.log(`Test user created: ${email}`);

      // Go to login page
      await page.goto("/auth/login");
      console.log("Navigated to login page");

      // Take screenshot for debugging
      await page.screenshot({ path: "e2e/screenshots/login-page.png" });

      // Fill in login form with valid credentials
      await page.fill('input[name="email"]', email);
      await page.fill('input[name="password"]', password);
      console.log("Filled in login form");

      // Submit the form
      await page.click('button[type="submit"]');
      console.log("Submitted login form");

      // Take screenshot after form submission
      await page.screenshot({ path: "e2e/screenshots/after-login-submit.png" });

      // Wait for any response or navigation
      console.log("Waiting for response after login...");
      await page.waitForTimeout(2000);

      // Check current URL
      const currentUrl = page.url();
      console.log("Current URL after login submission:", currentUrl);

      // Check for any error messages
      const errorElements = await page
        .locator(
          '.text-destructive, .text-red-500, .text-error, [role="alert"]'
        )
        .count();
      if (errorElements > 0) {
        console.log("Found error message on page after login attempt");
        const errorText = await page
          .locator(
            '.text-destructive, .text-red-500, .text-error, [role="alert"]'
          )
          .first()
          .textContent();
        console.log("Error message:", errorText);
      }

      // Take another screenshot
      await page.screenshot({ path: "e2e/screenshots/login-response.png" });

      // Check if we're already on the business select page
      if (currentUrl.includes("/business/select")) {
        console.log("Already redirected to business select page");
      } else {
        console.log("Waiting for redirect to business select page...");
        // Should redirect to business select page after login
        await page.waitForURL("/business/select", { timeout: 15000 });
        console.log("Redirected to business select page");
      }

      // Take screenshot of business select page
      await page.screenshot({
        path: "e2e/screenshots/business-select-page.png",
      });

      // Verify we're on the business select page
      await expect(page.getByText(/select a business/i)).toBeVisible({
        timeout: 5000,
      });
      console.log("Verified business select page content");

      // Test passed
      console.log("Login test passed successfully");
    } catch (error) {
      console.error("Login test failed:", error);
      // Take screenshot on failure
      await page.screenshot({ path: "e2e/screenshots/login-test-failure.png" });
      throw error;
    } finally {
      // Clean up the test user
      console.log(`Cleaning up test user: ${userId}`);
      await cleanupTestUser(userId);
    }
  }
);
