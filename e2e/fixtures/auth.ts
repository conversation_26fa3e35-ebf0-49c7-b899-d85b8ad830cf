import { test as base, expect } from "@playwright/test";
import { createClient } from "@supabase/supabase-js";
import type { Page } from "@playwright/test";
import * as fs from "fs";
import * as path from "path";
import * as dotenv from "dotenv";

// Load environment variables from .env.test file
const envPath = path.resolve(process.cwd(), ".env.test");
if (fs.existsSync(envPath)) {
  console.log("Loading environment variables from:", envPath);
  dotenv.config({ path: envPath });
} else {
  console.warn("No .env.test file found at:", envPath);
}

// Define custom test fixture types
type AuthFixtures = {
  authenticatedPage: Page;
  createTestUser: () => Promise<{
    email: string;
    password: string;
    userId: string;
  }>;
  cleanupTestUser: (userId: string) => Promise<void>;
};

// Create a supabase admin client for test data management
const createAdminClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error("Missing Supabase credentials for testing");
  }

  console.log("Creating admin client with URL:", supabaseUrl);

  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      persistSession: false,
      autoRefreshToken: true,
    },
  });
};

// Extend the base test with our custom fixtures
export const test = base.extend<AuthFixtures>({
  // Create a test user with a random email
  createTestUser: async ({}, use) => {
    const createUser = async () => {
      const adminClient = createAdminClient();
      const timestamp = Date.now();
      const testEmail = `test-${timestamp}@example.com`;
      const testPassword = "Test@123456";

      console.log(`Creating test user: ${testEmail}`);

      try {
        // Create a user with the admin client
        const { data: authData, error: authError } =
          await adminClient.auth.admin.createUser({
            email: testEmail,
            password: testPassword,
            email_confirm: true,
          });

        if (authError) {
          console.error(`Failed to create test user: ${authError.message}`);
          throw new Error(`Failed to create test user: ${authError.message}`);
        }

        if (!authData || !authData.user) {
          console.error("No user data returned from createUser");
          throw new Error("No user data returned from createUser");
        }

        console.log(
          `Test user created successfully with ID: ${authData.user.id}`
        );

        return {
          email: testEmail,
          password: testPassword,
          userId: authData.user.id,
        };
      } catch (error) {
        console.error("Error in createTestUser:", error);

        // Fallback: Try to create a user with signUp instead
        console.log("Trying alternative user creation method...");
        const { data: signUpData, error: signUpError } =
          await adminClient.auth.signUp({
            email: testEmail,
            password: testPassword,
            options: {
              emailRedirectTo: "http://localhost:3000/auth/callback",
            },
          });

        if (signUpError) {
          console.error(
            `Failed to create test user with signUp: ${signUpError.message}`
          );
          throw signUpError;
        }

        if (!signUpData || !signUpData.user) {
          throw new Error("No user data returned from signUp");
        }

        // Confirm the user's email
        const { error: updateError } =
          await adminClient.auth.admin.updateUserById(signUpData.user.id, {
            email_confirm: true,
          });

        if (updateError) {
          console.error(
            `Failed to confirm test user email: ${updateError.message}`
          );
          throw updateError;
        }

        console.log(`Test user created with signUp: ${signUpData.user.id}`);

        return {
          email: testEmail,
          password: testPassword,
          userId: signUpData.user.id,
        };
      }
    };

    await use(createUser);
  },

  // Clean up a test user
  cleanupTestUser: async ({}, use) => {
    const cleanup = async (userId: string) => {
      if (!userId) {
        console.warn("No user ID provided for cleanup");
        return;
      }

      console.log(`Cleaning up test user: ${userId}`);
      const adminClient = createAdminClient();

      try {
        // Delete the user
        const { error } = await adminClient.auth.admin.deleteUser(userId);

        if (error) {
          console.error(`Failed to delete test user: ${error.message}`);
        } else {
          console.log(`Test user ${userId} deleted successfully`);
        }
      } catch (error) {
        console.error(`Error during test user cleanup: ${error}`);
      }
    };

    await use(cleanup);
  },

  // Create a page that's already authenticated
  authenticatedPage: async ({ page, createTestUser }, use) => {
    let userId;

    try {
      // Create a test user
      const testUser = await createTestUser();
      userId = testUser.userId;
      const { email, password } = testUser;

      console.log(`Authenticating with test user: ${email}`);

      // Go to login page
      await page.goto("/auth/login");
      console.log("Navigated to login page");

      // Take screenshot for debugging
      await page.screenshot({ path: "e2e/screenshots/login-page.png" });

      // Fill in login form
      await page.fill('input[name="email"]', email);
      await page.fill('input[name="password"]', password);
      console.log("Filled in login form");

      // Submit the form
      await page.click('button[type="submit"]');
      console.log("Submitted login form");

      // Take screenshot after form submission
      await page.screenshot({ path: "e2e/screenshots/after-login-submit.png" });

      // Wait for any response or navigation
      console.log("Waiting for response after login...");
      await page.waitForTimeout(2000);

      // Check current URL
      const currentUrl = page.url();
      console.log("Current URL after login submission:", currentUrl);

      // Check for any error messages
      const errorElements = await page
        .locator(
          '.text-destructive, .text-red-500, .text-error, [role="alert"]'
        )
        .count();
      if (errorElements > 0) {
        console.log("Found error message on page after login attempt");
        const errorText = await page
          .locator(
            '.text-destructive, .text-red-500, .text-error, [role="alert"]'
          )
          .first()
          .textContent();
        console.log("Error message:", errorText);

        // Take a screenshot for debugging
        await page.screenshot({ path: "e2e/screenshots/login-error.png" });
        throw new Error(
          `Authentication failed: ${errorText || "Error message found on page"}`
        );
      }

      // Check if we're already on the business select page
      if (currentUrl.includes("/business/select")) {
        console.log("Already redirected to business select page");
      } else {
        console.log("Waiting for redirect to business select page...");
        // Should redirect to business select page after login
        await page.waitForURL("/business/select", { timeout: 15000 });
        console.log("Redirected to business select page");
      }

      // Take screenshot of business select page
      await page.screenshot({
        path: "e2e/screenshots/business-select-page.png",
      });

      // Make sure we're logged in by checking for logout button in the header
      try {
        const logoutButton = page.getByRole("button", { name: /logout/i });
        await expect(logoutButton).toBeVisible({ timeout: 5000 });
        console.log("Logout button is visible, user is authenticated");
      } catch (error) {
        console.error("Could not find logout button:", error);
        // Take a screenshot for debugging
        await page.screenshot({
          path: "e2e/screenshots/authentication-failed.png",
        });
        throw new Error("Authentication failed: Logout button not found");
      }

      // Use the authenticated page
      await use(page);
    } catch (error) {
      console.error("Error in authenticatedPage fixture:", error);
      // Take a screenshot for debugging
      await page.screenshot({
        path: "e2e/screenshots/authentication-error.png",
      });
      throw error;
    } finally {
      // Clean up the test user if it was created
      if (userId) {
        const adminClient = createAdminClient();
        try {
          await adminClient.auth.admin.deleteUser(userId);
          console.log(`Cleaned up test user ${userId} after authentication`);
        } catch (error) {
          console.error(`Failed to clean up test user ${userId}:`, error);
        }
      }
    }
  },
});
