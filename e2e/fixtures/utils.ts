import { expect, Page } from "@playwright/test";
import { createClient } from "@supabase/supabase-js";

// Create a supabase admin client for test data management
export const createAdminClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error("Missing Supabase credentials for testing");
  }

  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: { persistSession: false },
  });
};

// Create a test tenant for a user
export async function createTestTenant(
  userId: string,
  tenantName = "Test Gym"
) {
  const adminClient = createAdminClient();

  // Create a tenant
  const { data: tenant, error } = await adminClient
    .from("tenants")
    .insert({
      name: tenantName,
      slug: tenantName.toLowerCase().replace(/\s+/g, "-"),
      owner_id: userId,
    })
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create test tenant: ${error.message}`);
  }

  return tenant;
}

// Clean up a test tenant
export async function cleanupTestTenant(tenantId: string) {
  const adminClient = createAdminClient();

  // Delete the tenant
  const { error } = await adminClient
    .from("tenants")
    .delete()
    .eq("id", tenantId);

  if (error) {
    console.error(`Failed to delete test tenant: ${error.message}`);
  }
}

// Wait for toast notification and check its content
export async function expectToast(
  page: Page,
  text: string | RegExp,
  timeout = 5000
) {
  console.log(`Waiting for toast with text matching: ${text}`);

  // Take a screenshot before looking for toast
  await page.screenshot({
    path: `e2e/screenshots/toast-before-${Date.now()}.png`,
  });

  // Try different toast selectors
  const selectors = [
    '[role="status"]',
    ".sonner-toast",
    ".toast",
    ".notification",
    "[data-toast]",
    "[data-sonner-toast]",
    ".Toastify__toast",
    ".Toastify__toast-body",
  ];

  for (const selector of selectors) {
    console.log(`Trying toast selector: ${selector}`);
    const toast = page.locator(selector);

    try {
      const isVisible = await toast.isVisible({ timeout: 1000 });
      if (isVisible) {
        console.log(`Found visible toast with selector: ${selector}`);

        // Check if it contains the expected text
        const content = await toast.textContent();
        console.log(`Toast content: ${content}`);

        if (
          content &&
          (typeof text === "string"
            ? content.includes(text)
            : text.test(content))
        ) {
          console.log(`Toast contains expected text: ${text}`);
          // Take a screenshot of the toast
          await page.screenshot({
            path: `e2e/screenshots/toast-found-${Date.now()}.png`,
          });
          return;
        }
      }
    } catch {
      console.log(`Selector ${selector} not found or not visible`);
    }
  }

  // If we get here, we didn't find a matching toast
  console.error("No matching toast found");
  await page.screenshot({
    path: `e2e/screenshots/toast-not-found-${Date.now()}.png`,
  });
  throw new Error(`No toast found with text matching: ${text}`);
}

// Fill a form with data
export async function fillForm(page: Page, formData: Record<string, string>) {
  for (const [name, value] of Object.entries(formData)) {
    await page.fill(`[name="${name}"]`, value);
  }
}

// Take a screenshot with a descriptive name
export async function takeScreenshot(page: Page, name: string) {
  await page.screenshot({ path: `e2e/screenshots/${name}-${Date.now()}.png` });
}
