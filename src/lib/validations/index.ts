import { z } from "zod";

// Auth schemas
export const loginSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
  password: z.string().min(6, { message: "Password must be at least 6 characters" }),
});

export const registerSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
  password: z.string().min(6, { message: "Password must be at least 6 characters" }),
  confirmPassword: z.string(),
}).refine(data => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

// Tenant schemas
export const createTenantSchema = z.object({
  name: z.string().min(2, { message: "Business name must be at least 2 characters" }),
  slug: z.string().min(2, { message: "Slug must be at least 2 characters" })
    .regex(/^[a-z0-9-]+$/, { message: "Slug can only contain lowercase letters, numbers, and hyphens" }),
});

// Member schemas
export const memberSchema = z.object({
  first_name: z.string().min(2, { message: "First name must be at least 2 characters" }),
  last_name: z.string().min(2, { message: "Last name must be at least 2 characters" }),
  email: z.string().email({ message: "Please enter a valid email address" }),
  phone: z.string().optional(),
  status: z.enum(["active", "inactive", "pending"], {
    required_error: "Please select a status",
  }),
  notes: z.string().optional(),
});

// Subscription plan schemas
export const subscriptionPlanSchema = z.object({
  name: z.string().min(2, { message: "Plan name must be at least 2 characters" }),
  description: z.string().optional(),
  price: z.coerce.number().min(0, { message: "Price must be a positive number" }),
  billing_cycle: z.enum(["monthly", "quarterly", "biannual", "annual"], {
    required_error: "Please select a billing cycle",
  }),
  features: z.array(z.string()).optional(),
  is_active: z.boolean().default(true),
});

// Subscription schemas
export const subscriptionSchema = z.object({
  member_id: z.string().uuid({ message: "Please select a member" }),
  plan_id: z.string().uuid({ message: "Please select a plan" }),
  start_date: z.string(),
  end_date: z.string().optional(),
  status: z.enum(["active", "canceled", "expired", "pending"], {
    required_error: "Please select a status",
  }),
  auto_renew: z.boolean().default(true),
});

// Payment schemas
export const paymentSchema = z.object({
  subscription_id: z.string().uuid({ message: "Please select a subscription" }),
  amount: z.coerce.number().min(0, { message: "Amount must be a positive number" }),
  payment_method: z.enum(["credit_card", "debit_card", "bank_transfer", "cash"], {
    required_error: "Please select a payment method",
  }),
});
