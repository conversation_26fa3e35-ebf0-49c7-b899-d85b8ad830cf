import { createClient } from "@/utils/supabase/server";

/**
 * Fetches all tenants associated with a user
 * This function bypasses RLS policies by using the service role client
 */
export async function getUserTenants(userId: string) {
  if (!userId) {
    throw new Error("User ID is required");
  }

  // Use the service role client to bypass RLS policies
  const supabase = await createClient();

  try {
    // First try to get tenants from tenant_users table
    const { data: tenantUsers, error: tenantUsersError } = await supabase
      .from("tenant_users")
      .select(
        `
        tenant_id,
        role,
        tenants:tenant_id(
          id,
          name,
          slug,
          logo_url
        )
      `
      )
      .eq("user_id", userId);

    if (tenantUsersError) {
      console.error("Error fetching tenant users:", tenantUsersError);
    }

    // Get tenants where user is the owner
    const { data: ownedTenants, error: ownedTenantsError } = await supabase
      .from("tenants")
      .select("id, name, slug, logo_url")
      .eq("owner_id", userId);

    if (ownedTenantsError) {
      console.error("Error with direct tenant query:", ownedTenantsError);
      return [];
    }

    // Format the owned tenants to match the expected structure
    const formattedOwnedTenants = ownedTenants.map((tenant) => ({
      tenant_id: tenant.id,
      role: "admin", // Assume owner is admin
      tenants: {
        id: tenant.id,
        name: tenant.name,
        slug: tenant.slug,
        logo_url: tenant.logo_url,
      },
    }));

    // Combine tenant_users and owned tenants, removing duplicates
    const allTenants = formattedOwnedTenants;

    if (!tenantUsersError && tenantUsers && tenantUsers.length > 0) {
      // Add tenant_users tenants, avoiding duplicates
      const tenantIds = new Set(allTenants.map((t) => t.tenant_id));

      for (const tenant of tenantUsers) {
        if (!tenantIds.has(tenant.tenant_id)) {
          allTenants.push(tenant);
          tenantIds.add(tenant.tenant_id);
        }
      }
    }

    return allTenants;
  } catch (error) {
    console.error("Error fetching user tenants:", error);
    return [];
  }
}
