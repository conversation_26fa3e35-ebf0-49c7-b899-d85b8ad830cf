import { createClient } from "@/utils/supabase/server";

interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
}

interface UpcomingRenewal {
  id: string;
  end_date: string | null;
  members: {
    id: string;
    first_name: string;
    last_name: string;
  };
  subscription_plans: SubscriptionPlan;
}

interface RecentPayment {
  id: string;
  amount: number;
  status: string;
  payment_date: string;
  payment_method: string;
  subscriptions: {
    id: string;
    members: {
      id: string;
      first_name: string;
      last_name: string;
    };
  };
}

interface DashboardStats {
  totalMembers: number;
  activeMembers: number;
  activeSubscriptions: number;
  upcomingRenewals: UpcomingRenewal[];
  recentPayments: RecentPayment[];
  revenueStats: {
    last30Days: number;
    averagePerMember: number;
  };
}

export async function getDashboardStats(
  tenantId: string
): Promise<DashboardStats> {
  try {
    const supabase = await createClient();

    // Get total members count
    let totalMembers = 0;
    try {
      const { count, error } = await supabase
        .from("tenant_members")
        .select("*", { count: "exact", head: true })
        .eq("tenant_id", tenantId);

      if (error) {
        console.error("Error fetching members count:", error);
      } else {
        totalMembers = count || 0;
      }
    } catch (error) {
      console.error("Exception fetching members count:", error);
    }

    // Get active members count
    let activeMembers = 0;
    try {
      const { count, error } = await supabase
        .from("tenant_members")
        .select("*", { count: "exact", head: true })
        .eq("tenant_id", tenantId)
        .eq("status", "active");

      if (error) {
        console.error("Error fetching active members count:", error);
      } else {
        activeMembers = count || 0;
      }
    } catch (error) {
      console.error("Exception fetching active members count:", error);
    }

    // Get active subscriptions
    let activeSubscriptions = 0;
    try {
      const { count, error } = await supabase
        .from("subscriptions")
        .select(
          `
          id,
          subscription_plans!inner(id)
        `,
          { count: "exact", head: true }
        )
        .eq("subscription_plans.tenant_id", tenantId)
        .eq("status", "active");

      if (error) {
        console.error("Error fetching active subscriptions count:", error);
      } else {
        activeSubscriptions = count || 0;
      }
    } catch (error) {
      console.error("Exception fetching active subscriptions count:", error);
    }

    // Get upcoming renewals (next 7 days)
    let upcomingRenewals: UpcomingRenewal[] = [];
    try {
      const nextWeek = new Date();
      nextWeek.setDate(nextWeek.getDate() + 7);

      const { data, error } = await supabase
        .from("subscriptions")
        .select(
          `
          id,
          end_date,
          members:member_id(
            id,
            first_name,
            last_name
          ),
          subscription_plans:plan_id(
            id,
            name,
            price,
            tenant_id
          )
        `
        )
        .eq("subscription_plans.tenant_id", tenantId)
        .eq("status", "active")
        .lte("end_date", nextWeek.toISOString())
        .gte("end_date", new Date().toISOString());

      if (error) {
        console.error("Error fetching upcoming renewals:", error);
      } else if (data) {
        // Transform the data to match the expected format
        upcomingRenewals = data.map((sub) => ({
          id: sub.id,
          end_date: sub.end_date,
          members: {
            id: sub.members.id,
            first_name: sub.members.first_name,
            last_name: sub.members.last_name,
          },
          subscription_plans: sub.subscription_plans,
        }));
      }
    } catch (error) {
      console.error("Exception fetching upcoming renewals:", error);
    }

    // Get recent payments
    let recentPayments: RecentPayment[] = [];
    try {
      // Query payments directly with proper tenant filtering using inner joins
      const { data, error } = await supabase
        .from("payments")
        .select(
          `
          id,
          amount,
          status,
          payment_date,
          payment_method,
          subscriptions!inner(
            id,
            subscription_plans!inner(
              tenant_id
            ),
            members:member_id(
              id,
              first_name,
              last_name
            )
          )
        `
        )
        .eq("subscriptions.subscription_plans.tenant_id", tenantId)
        .order("payment_date", { ascending: false })
        .limit(5);

      if (error) {
        console.error("Error fetching recent payments:", error);
      } else if (data) {
        // Transform the data to match the expected format
        recentPayments = data.map((payment) => ({
          id: payment.id,
          amount: payment.amount,
          status: payment.status,
          payment_date: payment.payment_date,
          payment_method: payment.payment_method,
          subscriptions: {
            id: payment.subscriptions.id,
            members: {
              id: payment.subscriptions.members.id,
              first_name: payment.subscriptions.members.first_name,
              last_name: payment.subscriptions.members.last_name,
            },
          },
        }));
      }
    } catch (error) {
      console.error("Exception fetching recent payments:", error);
    }

    // Calculate revenue stats
    let totalRevenue = 0;
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      // Query payments with proper tenant filtering using inner joins
      const { data, error } = await supabase
        .from("payments")
        .select(
          `
          amount,
          subscriptions!inner(
            id,
            subscription_plans!inner(
              id,
              tenant_id
            )
          )
        `
        )
        .eq("status", "succeeded")
        .eq("subscriptions.subscription_plans.tenant_id", tenantId)
        .gte("payment_date", thirtyDaysAgo.toISOString());

      if (error) {
        console.error("Error fetching revenue data:", error);
      } else if (data && data.length > 0) {
        // Calculate total revenue
        totalRevenue = data.reduce((sum, payment) => sum + payment.amount, 0);
      }
    } catch (error) {
      console.error("Exception fetching revenue data:", error);
    }

    return {
      totalMembers,
      activeMembers,
      activeSubscriptions,
      upcomingRenewals,
      recentPayments,
      revenueStats: {
        last30Days: totalRevenue,
        averagePerMember: activeMembers ? totalRevenue / activeMembers : 0,
      },
    };
  } catch (error) {
    console.error("Error in getDashboardStats:", error);
    // Return default values in case of error
    return {
      totalMembers: 0,
      activeMembers: 0,
      activeSubscriptions: 0,
      upcomingRenewals: [] as UpcomingRenewal[],
      recentPayments: [] as RecentPayment[],
      revenueStats: {
        last30Days: 0,
        averagePerMember: 0,
      },
    };
  }
}
