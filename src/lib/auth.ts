import { redirect } from "next/navigation";
import {
  getCurrentUser as getPrismaCurrentUser,
  getCurrentUserWithTenants,
  verifyTenantAccess,
} from "@/lib/auth-prisma";

export async function getCurrentUser() {
  try {
    const user = await getPrismaCurrentUser();
    return user;
  } catch (error) {
    console.error("Error getting current user:", error);
    return null;
  }
}

export async function requireAuth() {
  const user = await getCurrentUser();

  if (!user) {
    redirect("/auth/login");
  }

  return user;
}

export async function getUserTenants(userId: string) {
  try {
    const userWithTenants = await getCurrentUserWithTenants();
    return userWithTenants.tenantUsers.map((tenantUser) => ({
      tenant_id: tenantUser.tenantId,
      role: tenantUser.role,
      tenants: tenantUser.tenant,
    }));
  } catch (error) {
    console.error("Error getting user tenants:", error);
    return [];
  }
}

export async function requireTenantAccess(tenantId: string) {
  const user = await requireAuth();

  if (!tenantId) {
    console.error("requireTenantAccess called with no tenantId");
    redirect("/business/select");
  }

  try {
    const { tenant, role } = await verifyTenantAccess(tenantId);
    return {
      user,
      role,
    };
  } catch (error) {
    console.error("User doesn't have access to this tenant:", error);
    redirect("/business/select");
  }
}
