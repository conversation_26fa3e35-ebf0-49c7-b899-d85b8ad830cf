import { redirect } from "next/navigation";
import { createClient } from "@/utils/supabase/server";

export async function getCurrentUser() {
  try {
    // Create a Supabase client that handles auth automatically
    const supabase = await createClient();

    // Get the user with authentication from Supabase Auth server
    const { data: userData, error } = await supabase.auth.getUser();

    if (error || !userData.user) {
      console.error("Error getting authenticated user:", error);
      return null;
    }

    return userData.user;
  } catch (error) {
    console.error("Error getting current user:", error);
    return null;
  }
}

export async function requireAuth() {
  const user = await getCurrentUser();

  if (!user) {
    redirect("/auth/login");
  }

  return user;
}

export async function getUserTenants(userId: string) {
  // Import the helper function from the tenants module
  const { getUserTenants: fetchTenants } = await import(
    "@/lib/supabase/tenants"
  );
  return fetchTenants(userId);
}

export async function requireTenantAccess(tenantId: string) {
  const user = await requireAuth();

  if (!tenantId) {
    console.error("requireTenantAccess called with no tenantId");
    redirect("/business/select");
  }

  try {
    // Get all tenants for the user
    const tenants = await getUserTenants(user.id);

    // Find the tenant in the user's tenants
    const userTenant = tenants.find((tenant) => tenant.tenant_id === tenantId);

    if (!userTenant) {
      // Also check if the user is the owner of the tenant
      const { createClient } = await import("@/utils/supabase/server");
      const serviceClient = await createClient();

      const { data: ownedTenant, error: ownedTenantError } = await serviceClient
        .from("tenants")
        .select("id")
        .eq("id", tenantId)
        .eq("owner_id", user.id)
        .single();

      if (ownedTenantError || !ownedTenant) {
        console.error("User doesn't have access to this tenant");
        redirect("/business/select");
      }

      return {
        user,
        role: "admin", // Owner is admin
      };
    }

    return {
      user,
      role: userTenant.role,
    };
  } catch (error) {
    console.error("Unexpected error checking tenant access:", error);
    redirect("/business/select");
  }
}
