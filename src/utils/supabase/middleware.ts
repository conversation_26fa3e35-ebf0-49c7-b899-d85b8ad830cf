import { NextRequest, NextResponse } from "next/server";
import { createServerClient } from "@supabase/ssr";
import { Database } from "@/types/supabase";

export async function updateSession(request: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll().map((cookie) => ({
            name: cookie.name,
            value: cookie.value,
          }));
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => {
            // Set cookie on request
            request.cookies.set({
              name,
              value,
              ...options,
            });

            // Create a new response with updated headers
            response = NextResponse.next({
              request: {
                headers: request.headers,
              },
            });

            // Set cookie on response
            response.cookies.set({
              name,
              value,
              ...options,
            });
          });
        },
      },
    }
  );

  // Get the user and set a custom header to indicate authentication status
  const {
    data: { user },
  } = await supabase.auth.getUser();

  // Set a custom header to indicate if the user is authenticated
  response.headers.set(
    "x-middleware-supabase-auth",
    user ? "authenticated" : "unauthenticated"
  );

  return response;
}
