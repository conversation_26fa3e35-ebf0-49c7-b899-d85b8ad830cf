import { createClient } from '@supabase/supabase-js';

// This script should be run with your Supabase service key
// It will apply the necessary changes for the public plans page feature

async function applyPublicPageChanges() {
  // Replace these with your actual Supabase URL and service key
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
  
  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('Missing Supabase URL or service key. Set NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables.');
    return;
  }

  // Create a Supabase client with the service key
  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  try {
    console.log('Adding public page columns to tenants table...');
    
    // Check if columns exist
    const { data: columns, error: columnsError } = await supabase
      .from('tenants')
      .select('public_page_enabled')
      .limit(1);
    
    if (columnsError && columnsError.message.includes('column "public_page_enabled" does not exist')) {
      // Add columns if they don't exist
      const { error: alterError } = await supabase.rpc('execute_sql', {
        sql: `
          ALTER TABLE tenants ADD COLUMN IF NOT EXISTS public_page_enabled BOOLEAN NOT NULL DEFAULT FALSE;
          ALTER TABLE tenants ADD COLUMN IF NOT EXISTS public_page_title TEXT;
          ALTER TABLE tenants ADD COLUMN IF NOT EXISTS public_page_description TEXT;
        `
      });
      
      if (alterError) {
        throw new Error(`Failed to add columns: ${alterError.message}`);
      }
      
      console.log('Columns added successfully.');
    } else {
      console.log('Columns already exist.');
    }
    
    console.log('Creating RLS policies...');
    
    // Create RLS policies
    const { error: policyError } = await supabase.rpc('execute_sql', {
      sql: `
        -- Drop existing policies if they exist
        DROP POLICY IF EXISTS tenant_public_select_policy ON tenants;
        DROP POLICY IF EXISTS subscription_plans_public_select_policy ON subscription_plans;

        -- Create a policy to allow public access to tenant information for tenants with public_page_enabled=true
        CREATE POLICY tenant_public_select_policy ON tenants
          FOR SELECT
          USING (
            public_page_enabled = TRUE
          );

        -- Create a policy to allow public access to subscription plans for tenants with public_page_enabled=true
        CREATE POLICY subscription_plans_public_select_policy ON subscription_plans
          FOR SELECT
          USING (
            EXISTS (
              SELECT 1 FROM tenants
              WHERE tenants.id = subscription_plans.tenant_id
              AND tenants.public_page_enabled = TRUE
              AND subscription_plans.is_active = TRUE
            )
          );

        -- Make sure RLS is enabled on these tables
        ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
        ALTER TABLE subscription_plans ENABLE ROW LEVEL SECURITY;
      `
    });
    
    if (policyError) {
      throw new Error(`Failed to create policies: ${policyError.message}`);
    }
    
    console.log('RLS policies created successfully.');
    
    // Update a test tenant for testing
    console.log('Updating test tenant...');
    
    const { error: updateError } = await supabase
      .from('tenants')
      .update({
        public_page_enabled: true,
        public_page_title: 'Membership Plans',
        public_page_description: 'Choose the perfect membership plan for you'
      })
      .eq('slug', 'gym-anooj');
    
    if (updateError) {
      throw new Error(`Failed to update test tenant: ${updateError.message}`);
    }
    
    console.log('Test tenant updated successfully.');
    console.log('All changes applied successfully!');
    
  } catch (error) {
    console.error('Error applying changes:', error);
  }
}

// Run the function
applyPublicPageChanges();
