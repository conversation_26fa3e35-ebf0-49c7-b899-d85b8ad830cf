import { NextRequest, NextResponse } from "next/server";
import { updateSession } from "@/utils/supabase/middleware";

export async function middleware(request: NextRequest) {
  // Update the session
  const response = await updateSession(request);

  // Get the pathname from the request
  const { pathname } = request.nextUrl;

  // Define public routes that don't require authentication
  const publicRoutes = [
    "/",
    "/auth/login",
    "/auth/register",
    "/auth/callback",
    "/p",
    "/member",
    "/member/login",
    "/member/register",
    "/member/reset-password",
  ];

  // Check if the user is authenticated
  // We'll rely on the updateSession function to handle authentication
  // The response already has the updated session
  const isAuthenticated =
    response.headers.get("x-middleware-supabase-auth") === "authenticated";

  // Check if the route is public
  const isPublicRoute = publicRoutes.some(
    (route) => pathname === route || pathname.startsWith(route + "/")
  );

  // If user is authenticated and trying to access auth pages, redirect to business selection
  // But don't redirect for public plan pages (/p/...) or member login pages
  if (
    isAuthenticated &&
    isPublicRoute &&
    pathname !== "/" &&
    pathname !== "/auth/callback" &&
    !pathname.startsWith("/p/") &&
    !pathname.startsWith("/member/")
  ) {
    return NextResponse.redirect(new URL("/business/select", request.url));
  }

  // Special case for member dashboard - must be authenticated
  if (pathname.startsWith("/member/dashboard") && !isAuthenticated) {
    return NextResponse.redirect(new URL("/member/login", request.url));
  }

  // Special case for member profile - must be authenticated
  if (pathname.startsWith("/member/profile") && !isAuthenticated) {
    return NextResponse.redirect(new URL("/member/login", request.url));
  }

  // If user is not authenticated and trying to access protected routes
  if (!isAuthenticated && !isPublicRoute) {
    return NextResponse.redirect(new URL("/auth/login", request.url));
  }

  // Special case for tenant creation page - must be authenticated
  if (pathname === "/tenant/create" && !isAuthenticated) {
    return NextResponse.redirect(new URL("/auth/login", request.url));
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public (public files)
     * - api (API routes that handle their own auth)
     */
    "/((?!_next/static|_next/image|favicon.ico|public|api).*)",
  ],
};
