export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export interface Database {
  public: {
    Tables: {
      tenants: {
        Row: {
          id: string;
          created_at: string;
          name: string;
          slug: string;
          logo_url: string | null;
          owner_id: string;
        };
        Insert: {
          id?: string;
          created_at?: string;
          name: string;
          slug: string;
          logo_url?: string | null;
          owner_id: string;
        };
        Update: {
          id?: string;
          created_at?: string;
          name?: string;
          slug?: string;
          logo_url?: string | null;
          owner_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "tenants_owner_id_fkey";
            columns: ["owner_id"];
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
      tenant_users: {
        Row: {
          id: string;
          created_at: string;
          tenant_id: string;
          user_id: string;
          role: string;
        };
        Insert: {
          id?: string;
          created_at?: string;
          tenant_id: string;
          user_id: string;
          role: string;
        };
        Update: {
          id?: string;
          created_at?: string;
          tenant_id?: string;
          user_id?: string;
          role?: string;
        };
        Relationships: [
          {
            foreignKeyName: "tenant_users_tenant_id_fkey";
            columns: ["tenant_id"];
            referencedRelation: "tenants";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "tenant_users_user_id_fkey";
            columns: ["user_id"];
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
      members: {
        Row: {
          id: string;
          created_at: string;
          first_name: string;
          last_name: string;
          email: string;
          phone: string | null;
          auth_id: string | null;
          password_set: boolean;
        };
        Insert: {
          id?: string;
          created_at?: string;
          first_name: string;
          last_name: string;
          email: string;
          phone?: string | null;
          auth_id?: string | null;
          password_set?: boolean;
        };
        Update: {
          id?: string;
          created_at?: string;
          first_name?: string;
          last_name?: string;
          email?: string;
          phone?: string | null;
          auth_id?: string | null;
          password_set?: boolean;
        };
        Relationships: [
          {
            foreignKeyName: "members_auth_id_fkey";
            columns: ["auth_id"];
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
      tenant_members: {
        Row: {
          id: string;
          created_at: string;
          tenant_id: string;
          member_id: string;
          status: string;
          notes: string | null;
        };
        Insert: {
          id?: string;
          created_at?: string;
          tenant_id: string;
          member_id: string;
          status: string;
          notes?: string | null;
        };
        Update: {
          id?: string;
          created_at?: string;
          tenant_id?: string;
          member_id?: string;
          status?: string;
          notes?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "tenant_members_tenant_id_fkey";
            columns: ["tenant_id"];
            referencedRelation: "tenants";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "tenant_members_member_id_fkey";
            columns: ["member_id"];
            referencedRelation: "members";
            referencedColumns: ["id"];
          }
        ];
      };
      subscription_plans: {
        Row: {
          id: string;
          created_at: string;
          tenant_id: string;
          name: string;
          description: string | null;
          price: number;
          billing_cycle: string;
          features: Json | null;
          is_active: boolean;
        };
        Insert: {
          id?: string;
          created_at?: string;
          tenant_id: string;
          name: string;
          description?: string | null;
          price: number;
          billing_cycle: string;
          features?: Json | null;
          is_active?: boolean;
        };
        Update: {
          id?: string;
          created_at?: string;
          tenant_id?: string;
          name?: string;
          description?: string | null;
          price?: number;
          billing_cycle?: string;
          features?: Json | null;
          is_active?: boolean;
        };
        Relationships: [
          {
            foreignKeyName: "subscription_plans_tenant_id_fkey";
            columns: ["tenant_id"];
            referencedRelation: "tenants";
            referencedColumns: ["id"];
          }
        ];
      };
      subscriptions: {
        Row: {
          id: string;
          created_at: string;
          member_id: string;
          plan_id: string;
          start_date: string;
          end_date: string | null;
          status: string;
          auto_renew: boolean;
        };
        Insert: {
          id?: string;
          created_at?: string;
          member_id: string;
          plan_id: string;
          start_date: string;
          end_date?: string | null;
          status: string;
          auto_renew?: boolean;
        };
        Update: {
          id?: string;
          created_at?: string;
          member_id?: string;
          plan_id?: string;
          start_date?: string;
          end_date?: string | null;
          status?: string;
          auto_renew?: boolean;
        };
        Relationships: [
          {
            foreignKeyName: "subscriptions_member_id_fkey";
            columns: ["member_id"];
            referencedRelation: "members";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "subscriptions_plan_id_fkey";
            columns: ["plan_id"];
            referencedRelation: "subscription_plans";
            referencedColumns: ["id"];
          }
        ];
      };
      payments: {
        Row: {
          id: string;
          created_at: string;
          subscription_id: string;
          amount: number;
          status: string;
          payment_date: string;
          payment_method: string;
          transaction_id: string | null;
        };
        Insert: {
          id?: string;
          created_at?: string;
          subscription_id: string;
          amount: number;
          status: string;
          payment_date: string;
          payment_method: string;
          transaction_id?: string | null;
        };
        Update: {
          id?: string;
          created_at?: string;
          subscription_id?: string;
          amount?: number;
          status?: string;
          payment_date?: string;
          payment_method?: string;
          transaction_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "payments_subscription_id_fkey";
            columns: ["subscription_id"];
            referencedRelation: "subscriptions";
            referencedColumns: ["id"];
          }
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
}
