"use client";

import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import {
  BarChart3,
  Users,
  CreditCard,
  Receipt,
  Package,
  Settings,
  Globe,
} from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";

interface DashboardSidebarProps {
  tenantSlug?: string;
}

export function DashboardSidebar({ tenantSlug }: DashboardSidebarProps) {
  const pathname = usePathname();
  const router = useRouter();

  const routes = [
    {
      href: `/tenant/${tenantSlug}`,
      label: "Dashboard",
      icon: BarChart3,
      active: pathname === `/tenant/${tenantSlug}`,
    },
  ];

  if (tenantSlug) {
    routes.push(
      {
        href: `/tenant/${tenantSlug}/members`,
        label: "Members",
        icon: Users,
        active: pathname.includes(`/tenant/${tenantSlug}/members`),
      },
      {
        href: `/tenant/${tenantSlug}/plans`,
        label: "Plans",
        icon: Package,
        active: pathname.includes(`/tenant/${tenantSlug}/plans`),
      },
      {
        href: `/tenant/${tenantSlug}/subscriptions`,
        label: "Subscriptions",
        icon: CreditCard,
        active: pathname.includes(`/tenant/${tenantSlug}/subscriptions`),
      },
      {
        href: `/tenant/${tenantSlug}/payments`,
        label: "Payments",
        icon: Receipt,
        active: pathname.includes(`/tenant/${tenantSlug}/payments`),
      }
    );
  }

  // Settings routes
  const settingsRoutes = tenantSlug
    ? [
        {
          href: `/tenant/${tenantSlug}/settings/public-page`,
          label: "Public Page",
          icon: Globe,
          active: pathname.includes(
            `/tenant/${tenantSlug}/settings/public-page`
          ),
        },
      ]
    : [];

  return (
    <aside className="hidden h-full w-64 flex-col border-r bg-background md:flex">
      <div className="flex flex-col gap-2 p-4">
        {/* Main routes */}
        {routes.map((route) => (
          <Button
            key={route.href}
            variant={route.active ? "default" : "ghost"}
            className={cn(
              "justify-start w-full",
              route.active ? "bg-primary text-primary-foreground" : ""
            )}
            asChild
          >
            <Link
              href={route.href}
              className="flex w-full items-center"
              onMouseEnter={() => router.prefetch(route.href)}
            >
              <route.icon className="mr-2 h-5 w-5" />
              {route.label}
            </Link>
          </Button>
        ))}

        {/* Settings section */}
        {settingsRoutes.length > 0 && (
          <>
            <div className="relative my-3">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">
                  Settings
                </span>
              </div>
            </div>

            {settingsRoutes.map((route) => (
              <Button
                key={route.href}
                variant={route.active ? "default" : "ghost"}
                className={cn(
                  "justify-start w-full",
                  route.active ? "bg-primary text-primary-foreground" : ""
                )}
                asChild
              >
                <Link
                  href={route.href}
                  className="flex w-full items-center"
                  onMouseEnter={() => router.prefetch(route.href)}
                >
                  <route.icon className="mr-2 h-5 w-5" />
                  {route.label}
                </Link>
              </Button>
            ))}
          </>
        )}
      </div>
    </aside>
  );
}
