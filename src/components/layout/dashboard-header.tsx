"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { LogOut, Menu } from "lucide-react";
import { useAuth } from "@/components/providers/auth-provider";

import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>etContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { getInitials } from "@/lib/utils";

interface DashboardHeaderProps {
  user: {
    id: string;
    email: string;
  };
  tenants?: Array<{
    tenant_id: string;
    role: string;
    tenants: {
      id: string;
      name: string;
      slug: string;
      logo_url: string | null;
    };
  }>;
  currentTenant?: {
    id: string;
    name: string;
    slug: string;
    logo_url: string | null;
  };
}

export function DashboardHeader({
  user,
  tenants = [],
  currentTenant,
}: DashboardHeaderProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const { logout } = useAuth();

  const handleLogout = async () => {
    setIsLoading(true);
    try {
      await logout();
    } catch (error) {
      toast.error("Failed to logout");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <header className="sticky top-0 z-40 w-full border-b bg-background">
      <div className="container mx-auto flex h-16 items-center justify-between px-4 py-4 md:px-6">
        <div className="flex items-center gap-4">
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="icon" className="md:hidden">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left">
              <SheetHeader className="pb-4">
                <SheetTitle>Menu</SheetTitle>
              </SheetHeader>
              <nav className="grid gap-2">
                {currentTenant && (
                  <>
                    <Link
                      href={`/tenant/${currentTenant.slug}/`}
                      className="flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground"
                      onMouseEnter={() =>
                        router.prefetch(`/tenant/${currentTenant.slug}/`)
                      }
                    >
                      Dashboard
                    </Link>
                    <Link
                      href={`/tenant/${currentTenant.slug}/members`}
                      className="flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground"
                      onMouseEnter={() =>
                        router.prefetch(`/tenant/${currentTenant.slug}/members`)
                      }
                    >
                      Members
                    </Link>
                    <Link
                      href={`/tenant/${currentTenant.slug}/plans`}
                      className="flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground"
                      onMouseEnter={() =>
                        router.prefetch(`/tenant/${currentTenant.slug}/plans`)
                      }
                    >
                      Subscription Plans
                    </Link>
                    <Link
                      href={`/tenant/${currentTenant.slug}/subscriptions`}
                      className="flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground"
                      onMouseEnter={() =>
                        router.prefetch(
                          `/tenant/${currentTenant.slug}/subscriptions`
                        )
                      }
                    >
                      Subscriptions
                    </Link>
                    <Link
                      href={`/tenant/${currentTenant.slug}/payments`}
                      className="flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground"
                      onMouseEnter={() =>
                        router.prefetch(
                          `/tenant/${currentTenant.slug}/payments`
                        )
                      }
                    >
                      Payments
                    </Link>
                  </>
                )}
              </nav>
            </SheetContent>
          </Sheet>
          <Link
            href={`/tenant/${currentTenant?.slug}`}
            className="flex items-center gap-2"
          >
            <span className="text-xl font-bold">GymSaaS</span>
          </Link>
        </div>
        <div className="flex items-center gap-4">
          {currentTenant && (
            <div className="hidden md:block">
              <span className="font-medium">{currentTenant.name}</span>
            </div>
          )}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="relative rounded-full"
              >
                <Avatar>
                  <AvatarFallback>
                    {getInitials(user.email.split("@")[0])}
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuItem disabled>{user.email}</DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuLabel>Switch Tenant</DropdownMenuLabel>
              {tenants.map((tenant) => (
                <DropdownMenuItem
                  key={tenant.tenant_id}
                  asChild
                  disabled={currentTenant?.id === tenant.tenant_id}
                >
                  <Link
                    href={`/tenant/${tenant.tenants.slug}`}
                    onMouseEnter={() =>
                      router.prefetch(`/tenant/${tenant.tenants.slug}`)
                    }
                  >
                    {tenant.tenants.name}
                  </Link>
                </DropdownMenuItem>
              ))}
              <DropdownMenuItem asChild>
                <Link
                  href="/tenant/create"
                  onMouseEnter={() => router.prefetch("/tenant/create")}
                >
                  Create New Tenant
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={handleLogout}
                disabled={isLoading}
                className="text-destructive focus:text-destructive"
              >
                <LogOut className="mr-2 h-4 w-4" />
                <span>Logout</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
