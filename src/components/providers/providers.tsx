"use client";

import { ToastProvider } from "./toast-provider";
import { AuthProvider } from "./auth-provider";
import { MemberAuthProvider } from "./member-auth-provider";

interface ProvidersProps {
  children: React.ReactNode;
}

export function Providers({ children }: ProvidersProps) {
  return (
    <>
      <ToastProvider />
      <AuthProvider>
        <MemberAuthProvider>{children}</MemberAuthProvider>
      </AuthProvider>
    </>
  );
}
