"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Session, User } from "@supabase/supabase-js";
import { createClient } from "@/utils/supabase/client";
import { getMemberByAuthId } from "@/app/actions/member-auth";

type MemberAuthContextType = {
  user: User | null;
  member: any | null;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
};

const MemberAuthContext = createContext<MemberAuthContextType | undefined>(undefined);

export function MemberAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [member, setMember] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    // Set up auth state listener
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(
      async (_event: string, session: Session | null) => {
        if (session) {
          setUser(session.user);
          
          // Get the member data
          const memberResult = await getMemberByAuthId(session.user.id);
          if (memberResult.success && memberResult.data) {
            setMember(memberResult.data);
          } else {
            // If no member found, this might be a tenant user, not a member
            setMember(null);
          }
        } else {
          setUser(null);
          setMember(null);
        }
        setIsLoading(false);
      }
    );

    // Get authenticated user
    const initializeAuth = async () => {
      try {
        console.log("Initializing member auth...");
        const { data: userData, error } = await supabase.auth.getUser();

        if (error) {
          console.error("Error getting authenticated user:", error);
          return;
        }

        if (userData.user) {
          console.log("Auth user:", `User: ${userData.user.email}`);
          setUser(userData.user);
          
          // Get the member data
          const memberResult = await getMemberByAuthId(userData.user.id);
          if (memberResult.success && memberResult.data) {
            setMember(memberResult.data);
            console.log("Member set from authenticated data:", memberResult.data.email);
          } else {
            // If no member found, this might be a tenant user, not a member
            setMember(null);
            console.log("No member found for this auth user");
          }
        } else {
          console.log("No authenticated user found");
        }
      } catch (error) {
        console.error("Error getting authenticated user:", error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();

    // Clean up subscription
    return () => {
      subscription.unsubscribe();
    };
  }, [supabase.auth]);

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw new Error(error.message);
      }

      toast.success("Logged in successfully");
      router.push("/member/dashboard");
    } catch (error) {
      console.error("Login error:", error);
      toast.error(error instanceof Error ? error.message : "Failed to login");
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    setIsLoading(true);
    try {
      const { error } = await supabase.auth.signOut();

      if (error) {
        throw new Error(error.message);
      }

      toast.success("Logged out successfully");
      router.push("/member/login");
    } catch (error) {
      console.error("Logout error:", error);
      toast.error(error instanceof Error ? error.message : "Failed to logout");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <MemberAuthContext.Provider value={{ user, member, isLoading, login, logout }}>
      {children}
    </MemberAuthContext.Provider>
  );
}

export function useMemberAuth() {
  const context = useContext(MemberAuthContext);
  if (context === undefined) {
    throw new Error("useMemberAuth must be used within a MemberAuthProvider");
  }
  return context;
}
