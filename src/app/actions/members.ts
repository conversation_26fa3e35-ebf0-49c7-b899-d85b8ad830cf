"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";
import { cache } from "react";
import { prisma } from "@/lib/prisma";
import { verifyTenantAccess } from "@/lib/auth-prisma";

// Define the member schema for validation
const memberSchema = z.object({
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().optional(),
});

// Define the tenant member schema for validation
const tenantMemberSchema = z.object({
  tenant_id: z.string().uuid(),
  member_id: z.string().uuid().optional(), // Optional because it might be created
  status: z.enum(["active", "inactive", "pending"]),
  notes: z.string().optional(),
});

// Combined schema for creating a new member in a tenant
const createMemberSchema = z.object({
  tenant_id: z.string().uuid(),
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().optional(),
  status: z.enum(["active", "inactive", "pending"]),
  notes: z.string().optional(),
});

type MemberData = z.infer<typeof memberSchema>;
type TenantMemberData = z.infer<typeof tenantMemberSchema>;
type CreateMemberData = z.infer<typeof createMemberSchema>;

/**
 * Create a new member
 */
export async function createMember(data: CreateMemberData) {
  try {
    // Validate the data
    const validatedData = createMemberSchema.parse(data);

    // Verify tenant access
    await verifyTenantAccess(validatedData.tenant_id);

    // Extract member data and tenant-specific data
    const { tenant_id, status, notes, ...memberData } = validatedData;

    // Use Prisma transaction to handle member creation/update and tenant association
    const result = await prisma.$transaction(async (tx) => {
      // First, check if the member already exists
      const existingMember = await tx.member.findUnique({
        where: { email: memberData.email },
      });

      let member;

      if (existingMember) {
        // Member already exists, update the member data to ensure it's current
        member = await tx.member.update({
          where: { id: existingMember.id },
          data: {
            firstName: memberData.first_name,
            lastName: memberData.last_name,
            phone: memberData.phone,
          },
        });
      } else {
        // Member doesn't exist, create a new one
        member = await tx.member.create({
          data: {
            firstName: memberData.first_name,
            lastName: memberData.last_name,
            email: memberData.email,
            phone: memberData.phone,
          },
        });
      }

      // Create the tenant_member association
      const tenantMember = await tx.tenantMember.create({
        data: {
          tenantId: tenant_id,
          memberId: member.id,
          status,
          notes,
        },
      });

      // Get the complete member data with tenant association
      const completeData = await tx.member.findUnique({
        where: { id: member.id },
        include: {
          tenantMembers: {
            where: { tenantId: tenant_id },
          },
        },
      });

      return completeData;
    });

    // Revalidate the members page to update the UI
    revalidatePath(`/tenant/${tenant_id}/members`);

    return { success: true, data: result };
  } catch (error) {
    console.error("Error creating member:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create member",
    };
  }
}

/**
 * Update an existing member
 */
export async function updateMember(
  id: string,
  tenantId: string,
  data: Partial<MemberData & { status?: string; notes?: string }>
) {
  try {
    // Verify tenant access
    await verifyTenantAccess(tenantId);

    // Extract tenant-specific data and member data
    const { status, notes, ...memberData } = data;

    // Use Prisma transaction to update both member and tenant-specific data
    const result = await prisma.$transaction(async (tx) => {
      // First, update the member's global information if provided
      if (Object.keys(memberData).length > 0) {
        await tx.member.update({
          where: { id },
          data: {
            ...(memberData.first_name && { firstName: memberData.first_name }),
            ...(memberData.last_name && { lastName: memberData.last_name }),
            ...(memberData.email && { email: memberData.email }),
            ...(memberData.phone !== undefined && { phone: memberData.phone }),
          },
        });
      }

      // Then, update the tenant-specific information if provided
      if ((status !== undefined || notes !== undefined) && tenantId) {
        await tx.tenantMember.update({
          where: {
            tenantId_memberId: {
              tenantId,
              memberId: id,
            },
          },
          data: {
            ...(status !== undefined && { status }),
            ...(notes !== undefined && { notes }),
          },
        });
      }

      // Get the updated member data with tenant association
      const updatedMember = await tx.member.findUnique({
        where: { id },
        include: {
          tenantMembers: {
            where: { tenantId },
          },
        },
      });

      return updatedMember;
    });

    // Revalidate the member pages to update the UI
    revalidatePath(`/tenant/${tenantId}/members`);
    revalidatePath(`/tenant/${tenantId}/members/${id}`);

    return { success: true, data: result };
  } catch (error) {
    console.error("Error updating member:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update member",
    };
  }
}

/**
 * Delete a member from a tenant
 */
export async function deleteMember(
  id: string,
  tenantId: string,
  deleteGlobally: boolean = false
) {
  try {
    // Verify tenant access
    await verifyTenantAccess(tenantId);

    if (deleteGlobally) {
      // Delete the member globally (will cascade to tenant_members)
      await prisma.member.delete({
        where: { id },
      });
    } else {
      // Only remove the association between the member and the tenant
      await prisma.tenantMember.delete({
        where: {
          tenantId_memberId: {
            tenantId,
            memberId: id,
          },
        },
      });
    }

    // Revalidate the members page to update the UI
    revalidatePath(`/tenant/${tenantId}/members`);

    return { success: true };
  } catch (error) {
    console.error("Error deleting member:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to delete member",
    };
  }
}

/**
 * Get all members for a tenant
 * This function is cached to improve performance
 */
export const getMembers = cache(async (tenantId: string) => {
  try {
    // Verify tenant access
    await verifyTenantAccess(tenantId);

    // Get all members for the tenant through the tenant_members junction table
    const tenantMembers = await prisma.tenantMember.findMany({
      where: { tenantId },
      include: {
        member: true,
      },
      orderBy: { createdAt: "desc" },
    });

    // Transform the data to match the expected format
    const transformedData = tenantMembers.map((item) => ({
      ...item.member,
      tenant_id: item.tenantId,
      status: item.status,
      notes: item.notes,
      tenant_member_id: item.id,
    }));

    return { success: true, data: transformedData };
  } catch (error) {
    console.error("Error getting members:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get members",
      data: [],
    };
  }
});

/**
 * Get a member by ID
 * This function is cached to improve performance
 */
export const getMemberById = cache(async (id: string, tenantId: string) => {
  try {
    // Verify tenant access
    await verifyTenantAccess(tenantId);

    // Get the member with tenant-specific data
    const member = await prisma.member.findUnique({
      where: { id },
      include: {
        tenantMembers: {
          where: { tenantId },
        },
      },
    });

    if (!member || member.tenantMembers.length === 0) {
      throw new Error("Member not found");
    }

    // Transform the data to match the expected format
    const tenantMember = member.tenantMembers[0];
    const transformedData = {
      ...member,
      tenant_id: tenantMember.tenantId,
      status: tenantMember.status,
      notes: tenantMember.notes,
      tenant_member_id: tenantMember.id,
    };

    return { success: true, data: transformedData };
  } catch (error) {
    console.error("Error getting member:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get member",
      data: null,
    };
  }
});

/**
 * Find a member by email
 * This function is cached to improve performance
 */
export const findMemberByEmail = cache(async (email: string) => {
  try {
    // Get the member with all tenant associations
    const member = await prisma.member.findUnique({
      where: { email },
      include: {
        tenantMembers: true,
      },
    });

    if (!member) {
      throw new Error("Member not found");
    }

    return { success: true, data: member };
  } catch (error) {
    console.error("Error finding member by email:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to find member",
      data: null,
    };
  }
});

/**
 * Get all tenants for a member
 * This function is cached to improve performance
 */
export const getMemberTenants = cache(async (memberId: string) => {
  try {
    // Get all tenants for the member
    const tenantMembers = await prisma.tenantMember.findMany({
      where: { memberId },
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            slug: true,
            logoUrl: true,
          },
        },
      },
    });

    // Transform the data to match the expected format
    const transformedData = tenantMembers.map((item) => ({
      ...item.tenant,
      status: item.status,
      tenant_member_id: item.id,
    }));

    return { success: true, data: transformedData };
  } catch (error) {
    console.error("Error getting member tenants:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to get member tenants",
      data: [],
    };
  }
});
