"use server";

import { revalidatePath } from "next/cache";
import { createClient } from "@/utils/supabase/server";
import { z } from "zod";
import { cache } from "react";

// Define the member schema for validation
const memberSchema = z.object({
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().optional(),
});

// Define the tenant member schema for validation
const tenantMemberSchema = z.object({
  tenant_id: z.string().uuid(),
  member_id: z.string().uuid().optional(), // Optional because it might be created
  status: z.enum(["active", "inactive", "pending"]),
  notes: z.string().optional(),
});

// Combined schema for creating a new member in a tenant
const createMemberSchema = z.object({
  tenant_id: z.string().uuid(),
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().optional(),
  status: z.enum(["active", "inactive", "pending"]),
  notes: z.string().optional(),
});

type MemberData = z.infer<typeof memberSchema>;
type TenantMemberData = z.infer<typeof tenantMemberSchema>;
type CreateMemberData = z.infer<typeof createMemberSchema>;

/**
 * Create a new member
 */
export async function createMember(data: CreateMemberData) {
  try {
    // Validate the data
    const validatedData = createMemberSchema.parse(data);

    // Create a Supabase client
    const supabase = await createClient();

    // Extract member data and tenant-specific data
    const { tenant_id, status, notes, ...memberData } = validatedData;

    // First, check if the member already exists
    const { data: existingMember, error: findError } = await supabase
      .from("members")
      .select("id")
      .eq("email", memberData.email)
      .maybeSingle();

    if (findError) {
      throw new Error(
        `Failed to check for existing member: ${findError.message}`
      );
    }

    let memberId;

    if (existingMember) {
      // Member already exists, use the existing ID
      memberId = existingMember.id;

      // Update the member data to ensure it's current
      const { error: updateError } = await supabase
        .from("members")
        .update(memberData)
        .eq("id", memberId);

      if (updateError) {
        throw new Error(
          `Failed to update existing member: ${updateError.message}`
        );
      }
    } else {
      // Member doesn't exist, create a new one
      const { data: newMember, error: insertError } = await supabase
        .from("members")
        .insert([memberData])
        .select()
        .single();

      if (insertError) {
        throw new Error(`Failed to create member: ${insertError.message}`);
      }

      memberId = newMember.id;
    }

    // Now create the tenant_member association
    const { data: tenantMember, error: associationError } = await supabase
      .from("tenant_members")
      .insert([
        {
          tenant_id,
          member_id: memberId,
          status,
          notes,
        },
      ])
      .select()
      .single();

    if (associationError) {
      throw new Error(
        `Failed to associate member with tenant: ${associationError.message}`
      );
    }

    // Get the complete member data with tenant association
    const { data: completeData, error: getError } = await supabase
      .from("members")
      .select(
        `
        *,
        tenant_members!inner(*)
      `
      )
      .eq("id", memberId)
      .eq("tenant_members.tenant_id", tenant_id)
      .single();

    if (getError) {
      throw new Error(
        `Failed to get complete member data: ${getError.message}`
      );
    }

    // Revalidate the members page to update the UI
    revalidatePath(`/tenant/${tenant_id}/members`);

    return { success: true, data: completeData };
  } catch (error) {
    console.error("Error creating member:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create member",
    };
  }
}

/**
 * Update an existing member
 */
export async function updateMember(
  id: string,
  tenantId: string,
  data: Partial<MemberData & { status?: string; notes?: string }>
) {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Extract tenant-specific data and member data
    const { status, notes, ...memberData } = data;

    // First, update the member's global information if provided
    if (Object.keys(memberData).length > 0) {
      const { error: memberError } = await supabase
        .from("members")
        .update(memberData)
        .eq("id", id);

      if (memberError) {
        throw new Error(`Failed to update member: ${memberError.message}`);
      }
    }

    // Then, update the tenant-specific information if provided
    if ((status !== undefined || notes !== undefined) && tenantId) {
      const tenantMemberData: any = {};
      if (status !== undefined) tenantMemberData.status = status;
      if (notes !== undefined) tenantMemberData.notes = notes;

      const { error: tenantMemberError } = await supabase
        .from("tenant_members")
        .update(tenantMemberData)
        .eq("member_id", id)
        .eq("tenant_id", tenantId);

      if (tenantMemberError) {
        throw new Error(
          `Failed to update tenant member: ${tenantMemberError.message}`
        );
      }
    }

    // Get the updated member data with tenant association
    const { data: updatedMember, error: getError } = await supabase
      .from("members")
      .select(
        `
        *,
        tenant_members!inner(*)
      `
      )
      .eq("id", id)
      .eq("tenant_members.tenant_id", tenantId)
      .single();

    if (getError) {
      throw new Error(`Failed to get updated member data: ${getError.message}`);
    }

    // Revalidate the member pages to update the UI
    revalidatePath(`/tenant/${tenantId}/members`);
    revalidatePath(`/tenant/${tenantId}/members/${id}`);

    return { success: true, data: updatedMember };
  } catch (error) {
    console.error("Error updating member:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update member",
    };
  }
}

/**
 * Delete a member from a tenant
 */
export async function deleteMember(
  id: string,
  tenantId: string,
  deleteGlobally: boolean = false
) {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    if (deleteGlobally) {
      // Delete the member globally (will cascade to tenant_members)
      const { error } = await supabase.from("members").delete().eq("id", id);

      if (error) {
        throw new Error(`Failed to delete member: ${error.message}`);
      }
    } else {
      // Only remove the association between the member and the tenant
      const { error } = await supabase
        .from("tenant_members")
        .delete()
        .eq("member_id", id)
        .eq("tenant_id", tenantId);

      if (error) {
        throw new Error(
          `Failed to remove member from tenant: ${error.message}`
        );
      }
    }

    // Revalidate the members page to update the UI
    revalidatePath(`/tenant/${tenantId}/members`);

    return { success: true };
  } catch (error) {
    console.error("Error deleting member:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to delete member",
    };
  }
}

/**
 * Get all members for a tenant
 * This function is cached to improve performance
 */
export const getMembers = cache(async (tenantId: string) => {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Get all members for the tenant through the tenant_members junction table
    const { data, error } = await supabase
      .from("tenant_members")
      .select(
        `
        *,
        member:member_id(
          id,
          first_name,
          last_name,
          email,
          phone,
          auth_id,
          password_set,
          created_at
        )
      `
      )
      .eq("tenant_id", tenantId)
      .order("created_at", { ascending: false });

    if (error) {
      throw new Error(`Failed to get members: ${error.message}`);
    }

    // Transform the data to match the expected format
    const transformedData =
      data?.map((item) => ({
        ...item.member,
        tenant_id: item.tenant_id,
        status: item.status,
        notes: item.notes,
        tenant_member_id: item.id,
      })) || [];

    return { success: true, data: transformedData };
  } catch (error) {
    console.error("Error getting members:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get members",
      data: [],
    };
  }
});

/**
 * Get a member by ID
 * This function is cached to improve performance
 */
export const getMemberById = cache(async (id: string, tenantId: string) => {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Get the member with tenant-specific data
    const { data, error } = await supabase
      .from("members")
      .select(
        `
        *,
        tenant_members!inner(*)
      `
      )
      .eq("id", id)
      .eq("tenant_members.tenant_id", tenantId)
      .single();

    if (error) {
      throw new Error(`Failed to get member: ${error.message}`);
    }

    // Transform the data to match the expected format
    const transformedData = {
      ...data,
      tenant_id: data.tenant_members[0].tenant_id,
      status: data.tenant_members[0].status,
      notes: data.tenant_members[0].notes,
      tenant_member_id: data.tenant_members[0].id,
    };

    return { success: true, data: transformedData };
  } catch (error) {
    console.error("Error getting member:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get member",
      data: null,
    };
  }
});

/**
 * Find a member by email
 * This function is cached to improve performance
 */
export const findMemberByEmail = cache(async (email: string) => {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Get the member
    const { data, error } = await supabase
      .from("members")
      .select(
        `
        *,
        tenant_members(*)
      `
      )
      .eq("email", email)
      .single();

    if (error) {
      throw new Error(`Failed to get member: ${error.message}`);
    }

    return { success: true, data };
  } catch (error) {
    console.error("Error finding member by email:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to find member",
      data: null,
    };
  }
});

/**
 * Get all tenants for a member
 * This function is cached to improve performance
 */
export const getMemberTenants = cache(async (memberId: string) => {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Get all tenants for the member
    const { data, error } = await supabase
      .from("tenant_members")
      .select(
        `
        *,
        tenant:tenant_id(
          id,
          name,
          slug,
          logo_url
        )
      `
      )
      .eq("member_id", memberId);

    if (error) {
      throw new Error(`Failed to get member tenants: ${error.message}`);
    }

    // Transform the data to match the expected format
    const transformedData =
      data?.map((item) => ({
        ...item.tenant,
        status: item.status,
        tenant_member_id: item.id,
      })) || [];

    return { success: true, data: transformedData };
  } catch (error) {
    console.error("Error getting member tenants:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to get member tenants",
      data: [],
    };
  }
});
