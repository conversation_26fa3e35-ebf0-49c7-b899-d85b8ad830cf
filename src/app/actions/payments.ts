"use server";

import { revalidatePath } from "next/cache";
import { createClient } from "@/utils/supabase/server";
import { z } from "zod";
import { cache } from "react";

// Define the payment schema for validation
const paymentSchema = z.object({
  subscription_id: z.string().uuid(),
  amount: z.number().min(0, "Amount must be a positive number"),
  status: z.enum(["pending", "succeeded", "failed", "refunded"]),
  payment_date: z.string(),
  payment_method: z.enum([
    "credit_card",
    "debit_card",
    "bank_transfer",
    "cash",
    "other",
  ]),
  transaction_id: z.string().optional(),
  notes: z.string().optional(),
});

type PaymentData = z.infer<typeof paymentSchema>;

/**
 * Create a new payment
 */
export async function createPayment(data: PaymentData) {
  try {
    // Validate the data
    const validatedData = paymentSchema.parse(data);

    // Create a Supabase client
    const supabase = await createClient();

    // Get the subscription to get the member_id
    const { data: subscription, error: subscriptionError } = await supabase
      .from("subscriptions")
      .select("member_id")
      .eq("id", validatedData.subscription_id)
      .single();

    if (subscriptionError || !subscription) {
      throw new Error(
        `Failed to find subscription: ${
          subscriptionError?.message || "Subscription not found"
        }`
      );
    }

    // Get the subscription plan to get the tenant_id
    const { data: subscriptionDetails, error: subscriptionDetailsError } =
      await supabase
        .from("subscriptions")
        .select("plan_id")
        .eq("id", validatedData.subscription_id)
        .single();

    if (subscriptionDetailsError || !subscriptionDetails) {
      throw new Error(
        `Failed to get subscription details: ${
          subscriptionDetailsError?.message || "Subscription details not found"
        }`
      );
    }

    // Get the tenant_id from the subscription plan
    const { data: plan, error: planError } = await supabase
      .from("subscription_plans")
      .select("tenant_id")
      .eq("id", subscriptionDetails.plan_id)
      .single();

    if (planError || !plan) {
      throw new Error(
        `Failed to find subscription plan: ${
          planError?.message || "Subscription plan not found"
        }`
      );
    }

    // Use the plan's tenant_id
    const tenantMember = { tenant_id: plan.tenant_id };

    // Insert the payment
    const { data: payment, error } = await supabase
      .from("payments")
      .insert([validatedData])
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create payment: ${error.message}`);
    }

    // Revalidate the payments page to update the UI
    revalidatePath(`/tenant/${tenantMember.tenant_id}/payments`);
    revalidatePath(
      `/tenant/${tenantMember.tenant_id}/subscriptions/${validatedData.subscription_id}`
    );

    return { success: true, data: payment };
  } catch (error) {
    console.error("Error creating payment:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to create payment",
    };
  }
}

/**
 * Update an existing payment
 */
export async function updatePayment(id: string, data: Partial<PaymentData>) {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Get the current payment to get the subscription_id
    const { data: currentPayment, error: fetchError } = await supabase
      .from("payments")
      .select("subscription_id")
      .eq("id", id)
      .single();

    if (fetchError || !currentPayment) {
      throw new Error(
        `Failed to find payment: ${fetchError?.message || "Payment not found"}`
      );
    }

    // Get the subscription to get the member_id
    const { data: subscription, error: subscriptionError } = await supabase
      .from("subscriptions")
      .select("member_id")
      .eq("id", currentPayment.subscription_id)
      .single();

    if (subscriptionError || !subscription) {
      throw new Error(
        `Failed to find subscription: ${
          subscriptionError?.message || "Subscription not found"
        }`
      );
    }

    // Get the subscription plan to get the tenant_id
    const { data: subscriptionDetails, error: subscriptionDetailsError } =
      await supabase
        .from("subscriptions")
        .select("plan_id")
        .eq("id", currentPayment.subscription_id)
        .single();

    if (subscriptionDetailsError || !subscriptionDetails) {
      throw new Error(
        `Failed to get subscription details: ${
          subscriptionDetailsError?.message || "Subscription details not found"
        }`
      );
    }

    // Get the tenant_id from the subscription plan
    const { data: plan, error: planError } = await supabase
      .from("subscription_plans")
      .select("tenant_id")
      .eq("id", subscriptionDetails.plan_id)
      .single();

    if (planError || !plan) {
      throw new Error(
        `Failed to find subscription plan: ${
          planError?.message || "Subscription plan not found"
        }`
      );
    }

    // Use the plan's tenant_id
    const tenantMember = { tenant_id: plan.tenant_id };

    // Update the payment
    const { data: payment, error } = await supabase
      .from("payments")
      .update(data)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update payment: ${error.message}`);
    }

    // Revalidate the payment pages to update the UI
    revalidatePath(`/tenant/${tenantMember.tenant_id}/payments`);
    revalidatePath(`/tenant/${tenantMember.tenant_id}/payments/${id}`);
    revalidatePath(
      `/tenant/${tenantMember.tenant_id}/subscriptions/${currentPayment.subscription_id}`
    );

    return { success: true, data: payment };
  } catch (error) {
    console.error("Error updating payment:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to update payment",
    };
  }
}

/**
 * Process a payment (simulate payment processing)
 */
export async function processPayment(subscriptionId: string, amount: number) {
  try {
    // Create a payment with pending status
    const paymentData = {
      subscription_id: subscriptionId,
      amount,
      status: "pending" as const,
      payment_date: new Date().toISOString(),
      payment_method: "credit_card" as const,
      transaction_id: `txn_${Math.random().toString(36).substring(2, 10)}`,
    };

    // Create the payment
    const result = await createPayment(paymentData);

    if (!result.success || !result.data) {
      throw new Error(result.error || "Failed to create payment");
    }

    // Simulate payment processing
    // In a real application, this would call a payment gateway
    const paymentId = result.data.id;

    // Update the payment status to succeeded
    const updateResult = await updatePayment(paymentId, {
      status: "succeeded",
    });

    if (!updateResult.success) {
      throw new Error(updateResult.error);
    }

    return { success: true, data: updateResult.data };
  } catch (error) {
    console.error("Error processing payment:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to process payment",
    };
  }
}

/**
 * Get all payments for a tenant
 * This function is cached to improve performance
 */
export const getPayments = cache(async (tenantId: string) => {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Get payments with proper tenant filtering using inner joins
    const { data, error } = await supabase
      .from("payments")
      .select(
        `
        *,
        subscriptions:subscription_id(
          id,
          member_id,
          plan_id,
          status,
          members:member_id(
            id,
            first_name,
            last_name,
            email,
            tenant_members!inner(
              tenant_id,
              status
            )
          ),
          subscription_plans!inner(
            id,
            name,
            price,
            tenant_id
          )
        )
      `
      )
      .eq("subscriptions.subscription_plans.tenant_id", tenantId)
      .order("payment_date", { ascending: false });

    if (error) {
      throw new Error(`Failed to get payments: ${error.message}`);
    }

    // No need to check for error here as we already handled the RPC error above

    // Transform the data to match the expected format in the client
    const transformedData =
      data?.map((payment) => {
        if (payment.subscriptions && payment.subscriptions.members) {
          // Extract tenant_members data
          const tenantMember =
            payment.subscriptions.members.tenant_members[0] || {};

          // Create a transformed payment object
          return {
            ...payment,
            subscriptions: {
              ...payment.subscriptions,
              members: {
                ...payment.subscriptions.members,
                tenant_id: tenantMember.tenant_id,
                status: tenantMember.status,
              },
            },
          };
        }
        return payment;
      }) || [];

    return { success: true, data: transformedData };
  } catch (error) {
    console.error("Error getting payments:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get payments",
      data: [],
    };
  }
});

/**
 * Get a payment by ID
 * This function is cached to improve performance
 */
export const getPaymentById = cache(async (id: string) => {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Get the payment
    const { data, error } = await supabase
      .from("payments")
      .select(
        `
        *,
        subscriptions:subscription_id(
          id,
          member_id,
          plan_id,
          status,
          start_date,
          end_date,
          auto_renew,
          members:member_id(
            id,
            first_name,
            last_name,
            email,
            tenant_members(
              tenant_id,
              status
            )
          ),
          subscription_plans:plan_id(
            id,
            name,
            price,
            billing_cycle,
            tenant_id
          )
        )
      `
      )
      .eq("id", id)
      .single();

    if (error) {
      throw new Error(`Failed to get payment: ${error.message}`);
    }

    // Transform the data to match the expected format in the client
    if (data && data.subscriptions && data.subscriptions.members) {
      // Extract tenant_members data
      const tenantMember = data.subscriptions.members.tenant_members?.[0] || {};

      // Create a transformed payment object
      const transformedData = {
        ...data,
        subscriptions: {
          ...data.subscriptions,
          members: {
            ...data.subscriptions.members,
            tenant_id: tenantMember.tenant_id,
            status: tenantMember.status,
          },
        },
      };

      return { success: true, data: transformedData };
    }

    return { success: true, data };
  } catch (error) {
    console.error("Error getting payment:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get payment",
      data: null,
    };
  }
});

/**
 * Get all payments for a subscription
 * This function is cached to improve performance
 */
export const getSubscriptionPayments = cache(async (subscriptionId: string) => {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Get all payments for the subscription
    const { data, error } = await supabase
      .from("payments")
      .select("*")
      .eq("subscription_id", subscriptionId)
      .order("payment_date", { ascending: false });

    if (error) {
      throw new Error(`Failed to get subscription payments: ${error.message}`);
    }

    return { success: true, data: data || [] };
  } catch (error) {
    console.error("Error getting subscription payments:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to get subscription payments",
      data: [],
    };
  }
});
