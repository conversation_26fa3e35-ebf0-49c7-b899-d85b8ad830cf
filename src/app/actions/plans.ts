"use server";

import { revalidatePath } from "next/cache";
import { createClient } from "@/utils/supabase/server";
import { z } from "zod";
import { cache } from "react";

// Define the plan schema for validation
const planSchema = z.object({
  tenant_id: z.string().uuid(),
  name: z.string().min(1, "Plan name is required"),
  description: z.string().optional(),
  price: z.number().min(0, "Price must be a positive number"),
  billing_cycle: z.enum([
    "monthly",
    "quarterly",
    "biannually",
    "annually",
    "biannual",
    "annual",
  ]),
  features: z.array(z.string()).optional(),
  is_active: z.boolean().default(true),
});

type PlanData = z.infer<typeof planSchema>;

/**
 * Create a new subscription plan
 */
export async function createPlan(data: PlanData) {
  try {
    // Validate the data
    const validatedData = planSchema.parse(data);

    // Create a Supabase client
    const supabase = await createClient();

    // Insert the plan
    const { data: plan, error } = await supabase
      .from("subscription_plans")
      .insert([validatedData])
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create plan: ${error.message}`);
    }

    // Revalidate the plans page to update the UI
    revalidatePath(`/tenant/${plan.tenant_id}/plans`);

    return { success: true, data: plan };
  } catch (error) {
    console.error("Error creating plan:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create plan",
    };
  }
}

/**
 * Update an existing subscription plan
 */
export async function updatePlan(
  id: string,
  tenantId: string,
  data: Partial<Omit<PlanData, "tenant_id">>
) {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Update the plan
    const { data: plan, error } = await supabase
      .from("subscription_plans")
      .update(data)
      .eq("id", id)
      .eq("tenant_id", tenantId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update plan: ${error.message}`);
    }

    // Revalidate the plan pages to update the UI
    revalidatePath(`/tenant/${tenantId}/plans`);
    revalidatePath(`/tenant/${tenantId}/plans/${id}`);

    return { success: true, data: plan };
  } catch (error) {
    console.error("Error updating plan:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update plan",
    };
  }
}

/**
 * Delete a subscription plan
 */
export async function deletePlan(id: string, tenantId: string) {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Delete the plan
    const { error } = await supabase
      .from("subscription_plans")
      .delete()
      .eq("id", id)
      .eq("tenant_id", tenantId);

    if (error) {
      throw new Error(`Failed to delete plan: ${error.message}`);
    }

    // Revalidate the plans page to update the UI
    revalidatePath(`/tenant/${tenantId}/plans`);

    return { success: true };
  } catch (error) {
    console.error("Error deleting plan:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to delete plan",
    };
  }
}

/**
 * Get all subscription plans for a tenant
 * This function is cached to improve performance
 */
export const getPlans = cache(async (tenantId: string) => {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Get all plans for the tenant
    const { data, error } = await supabase
      .from("subscription_plans")
      .select("*")
      .eq("tenant_id", tenantId)
      .order("name", { ascending: true });

    if (error) {
      throw new Error(`Failed to get plans: ${error.message}`);
    }

    return { success: true, data: data || [] };
  } catch (error) {
    console.error("Error getting plans:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get plans",
      data: [],
    };
  }
});

/**
 * Get a subscription plan by ID
 * This function is cached to improve performance
 */
export const getPlanById = cache(async (id: string, tenantId: string) => {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Get the plan
    const { data, error } = await supabase
      .from("subscription_plans")
      .select("*")
      .eq("id", id)
      .eq("tenant_id", tenantId)
      .single();

    if (error) {
      throw new Error(`Failed to get plan: ${error.message}`);
    }

    return { success: true, data };
  } catch (error) {
    console.error("Error getting plan:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get plan",
      data: null,
    };
  }
});
