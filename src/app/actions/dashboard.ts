"use server";

import { cache } from "react";
import { prisma } from "@/lib/prisma";
import { verifyTenantAccess } from "@/lib/auth-prisma";

interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
}

interface UpcomingRenewal {
  id: string;
  endDate: Date | null;
  member: {
    id: string;
    firstName: string;
    lastName: string;
  };
  plan: SubscriptionPlan;
}

interface RecentPayment {
  id: string;
  amount: number;
  status: string;
  paymentDate: Date;
  paymentMethod: string;
  subscription: {
    id: string;
    member: {
      id: string;
      firstName: string;
      lastName: string;
    };
  };
}

interface DashboardStats {
  totalMembers: number;
  activeMembers: number;
  activeSubscriptions: number;
  upcomingRenewals: UpcomingRenewal[];
  recentPayments: RecentPayment[];
  revenueStats: {
    last30Days: number;
    averagePerMember: number;
  };
}

export const getDashboardStats = cache(async (tenantId: string): Promise<DashboardStats> => {
  try {
    // Verify tenant access
    await verifyTenantAccess(tenantId);

    // Get total members count
    const totalMembers = await prisma.tenantMember.count({
      where: { tenantId },
    });

    // Get active members count
    const activeMembers = await prisma.tenantMember.count({
      where: {
        tenantId,
        status: "active",
      },
    });

    // Get active subscriptions count
    const activeSubscriptions = await prisma.subscription.count({
      where: {
        plan: {
          tenantId,
        },
        status: "active",
      },
    });

    // Get upcoming renewals (next 7 days)
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);

    const upcomingRenewalsData = await prisma.subscription.findMany({
      where: {
        plan: {
          tenantId,
        },
        status: "active",
        endDate: {
          lte: nextWeek,
          gte: new Date(),
        },
      },
      include: {
        member: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
        plan: {
          select: {
            id: true,
            name: true,
            price: true,
          },
        },
      },
      orderBy: {
        endDate: 'asc',
      },
    });

    const upcomingRenewals: UpcomingRenewal[] = upcomingRenewalsData.map((sub) => ({
      id: sub.id,
      endDate: sub.endDate,
      member: sub.member,
      plan: sub.plan,
    }));

    // Get recent payments (last 5)
    const recentPaymentsData = await prisma.payment.findMany({
      where: {
        subscription: {
          plan: {
            tenantId,
          },
        },
      },
      include: {
        subscription: {
          include: {
            member: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        },
      },
      orderBy: {
        paymentDate: 'desc',
      },
      take: 5,
    });

    const recentPayments: RecentPayment[] = recentPaymentsData.map((payment) => ({
      id: payment.id,
      amount: Number(payment.amount),
      status: payment.status,
      paymentDate: payment.paymentDate,
      paymentMethod: payment.paymentMethod,
      subscription: {
        id: payment.subscription.id,
        member: payment.subscription.member,
      },
    }));

    // Calculate revenue stats (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const revenueData = await prisma.payment.findMany({
      where: {
        status: "succeeded",
        paymentDate: {
          gte: thirtyDaysAgo,
        },
        subscription: {
          plan: {
            tenantId,
          },
        },
      },
      select: {
        amount: true,
      },
    });

    const totalRevenue = revenueData.reduce((sum, payment) => sum + Number(payment.amount), 0);
    const averagePerMember = activeMembers > 0 ? totalRevenue / activeMembers : 0;

    return {
      totalMembers,
      activeMembers,
      activeSubscriptions,
      upcomingRenewals,
      recentPayments,
      revenueStats: {
        last30Days: totalRevenue,
        averagePerMember,
      },
    };
  } catch (error) {
    console.error("Error getting dashboard stats:", error);
    return {
      totalMembers: 0,
      activeMembers: 0,
      activeSubscriptions: 0,
      upcomingRenewals: [],
      recentPayments: [],
      revenueStats: {
        last30Days: 0,
        averagePerMember: 0,
      },
    };
  }
});
