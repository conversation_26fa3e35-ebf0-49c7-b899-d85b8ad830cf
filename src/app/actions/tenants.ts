"use server";

import { revalidatePath } from "next/cache";
import { createClient } from "@/utils/supabase/server";
import { z } from "zod";
import { cache } from "react";

// Define the tenant schema for validation
const tenantSchema = z.object({
  name: z.string().min(1, "Tenant name is required"),
  slug: z.string().min(1, "Slug is required"),
  logo_url: z.string().optional(),
  public_page_enabled: z.boolean().optional(),
  public_page_title: z.string().optional(),
  public_page_description: z.string().optional(),
});

type TenantData = z.infer<typeof tenantSchema>;

/**
 * Create a new tenant
 */
export async function createTenant(data: TenantData) {
  try {
    // Validate the data
    const validatedData = tenantSchema.parse(data);

    // Create a Supabase client
    const supabase = await createClient();

    // Get the current user
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError || !userData.user) {
      throw new Error("Authentication required");
    }

    // Insert the tenant
    const { data: tenant, error: tenantError } = await supabase
      .from("tenants")
      .insert([
        {
          ...validatedData,
          owner_id: userData.user.id,
        },
      ])
      .select()
      .single();

    if (tenantError) {
      throw new Error(`Failed to create tenant: ${tenantError.message}`);
    }

    // Add the user as an admin of the tenant
    const { error: tenantUserError } = await supabase
      .from("tenant_users")
      .insert([
        {
          tenant_id: tenant.id,
          user_id: userData.user.id,
          role: "admin",
        },
      ]);

    if (tenantUserError) {
      // If adding the user as admin fails, delete the tenant
      await supabase.from("tenants").delete().eq("id", tenant.id);
      throw new Error(
        `Failed to add user as admin: ${tenantUserError.message}`
      );
    }

    // Revalidate the business selection page
    revalidatePath("/business/select");

    return { success: true, data: tenant };
  } catch (error) {
    console.error("Error creating tenant:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create tenant",
    };
  }
}

/**
 * Update an existing tenant
 */
export async function updateTenant(id: string, data: Partial<TenantData>) {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Get the current user
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError || !userData.user) {
      throw new Error("Authentication required");
    }

    // Check if the user is the owner or admin of the tenant
    const { data: tenantUser, error: tenantUserError } = await supabase
      .from("tenant_users")
      .select("role")
      .eq("tenant_id", id)
      .eq("user_id", userData.user.id)
      .single();

    const { data: ownedTenant, error: ownedTenantError } = await supabase
      .from("tenants")
      .select("owner_id")
      .eq("id", id)
      .single();

    if (
      (tenantUserError || !tenantUser || tenantUser.role !== "admin") &&
      (ownedTenantError ||
        !ownedTenant ||
        ownedTenant.owner_id !== userData.user.id)
    ) {
      throw new Error("You don't have permission to update this tenant");
    }

    // Update the tenant
    const { data: tenant, error: updateError } = await supabase
      .from("tenants")
      .update(data)
      .eq("id", id)
      .select()
      .single();

    if (updateError) {
      throw new Error(`Failed to update tenant: ${updateError.message}`);
    }

    // Revalidate the tenant pages
    revalidatePath("/business/select");
    revalidatePath(`/tenant/${tenant.slug}`);

    return { success: true, data: tenant };
  } catch (error) {
    console.error("Error updating tenant:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update tenant",
    };
  }
}

/**
 * Get all tenants for the current user
 * This function is cached to improve performance
 */
export const getUserTenants = cache(async () => {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Get the current user
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError || !userData.user) {
      throw new Error("Authentication required");
    }

    // Get all tenants for the user
    const { data, error } = await supabase
      .from("tenant_users")
      .select(
        `
        *,
        tenants(*)
      `
      )
      .eq("user_id", userData.user.id)
      .order("created_at", { ascending: false });

    if (error) {
      throw new Error(`Failed to get tenants: ${error.message}`);
    }

    return { success: true, data: data || [] };
  } catch (error) {
    console.error("Error getting tenants:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get tenants",
      data: [],
    };
  }
});

/**
 * Get a tenant by slug
 * This function is cached to improve performance
 */
export const getTenantBySlug = cache(async (slug: string) => {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Get the tenant
    const { data, error } = await supabase
      .from("tenants")
      .select("*")
      .eq("slug", slug)
      .single();

    if (error) {
      throw new Error(`Failed to get tenant: ${error.message}`);
    }

    return { success: true, data };
  } catch (error) {
    console.error("Error getting tenant:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get tenant",
      data: null,
    };
  }
});

/**
 * Update the public page settings for a tenant
 */
export async function updatePublicPageSettings(
  tenantId: string,
  data: {
    public_page_enabled: boolean;
    public_page_title?: string;
    public_page_description?: string;
  }
) {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Get the current user
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError || !userData.user) {
      throw new Error("Authentication required");
    }

    // Check if the user is the owner or admin of the tenant
    const { data: tenantUser, error: tenantUserError } = await supabase
      .from("tenant_users")
      .select("role")
      .eq("tenant_id", tenantId)
      .eq("user_id", userData.user.id)
      .single();

    const { data: ownedTenant, error: ownedTenantError } = await supabase
      .from("tenants")
      .select("owner_id, slug")
      .eq("id", tenantId)
      .single();

    if (
      (tenantUserError || !tenantUser || tenantUser.role !== "admin") &&
      (ownedTenantError ||
        !ownedTenant ||
        ownedTenant.owner_id !== userData.user.id)
    ) {
      throw new Error("You don't have permission to update this tenant");
    }

    // Update the tenant's public page settings
    const { data: tenant, error: updateError } = await supabase
      .from("tenants")
      .update({
        public_page_enabled: data.public_page_enabled,
        public_page_title: data.public_page_title,
        public_page_description: data.public_page_description,
      })
      .eq("id", tenantId)
      .select()
      .single();

    if (updateError) {
      throw new Error(
        `Failed to update public page settings: ${updateError.message}`
      );
    }

    // Revalidate the tenant pages
    revalidatePath(`/tenant/${tenant.slug}`);
    revalidatePath(`/tenant/${tenant.slug}/settings/public-page`);
    // Also revalidate the public page if it exists
    if (tenant.public_page_enabled) {
      revalidatePath(`/p/${tenant.slug}`);
    }

    return { success: true, data: tenant };
  } catch (error) {
    console.error("Error updating public page settings:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to update public page settings",
    };
  }
}
