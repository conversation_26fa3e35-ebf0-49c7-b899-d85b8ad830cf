"use server";

import { revalidatePath } from "next/cache";
import { createClient } from "@/utils/supabase/server";
import { z } from "zod";
import { cache } from "react";

// Define the subscription schema for validation
const subscriptionSchema = z.object({
  member_id: z.string().uuid(),
  plan_id: z.string().uuid(),
  start_date: z.string().optional(),
  end_date: z.string().optional(),
  status: z.enum([
    "active",
    "inactive",
    "pending",
    "cancelled",
    "canceled",
    "expired",
  ]),
  auto_renew: z.boolean().default(true),
  price_override: z.number().optional(),
  notes: z.string().optional(),
  current_tenant_id: z.string().uuid().optional(), // Optional tenant ID to help with member lookup
});

type SubscriptionData = z.infer<typeof subscriptionSchema>;

/**
 * Create a new subscription
 */
export async function createSubscription(data: SubscriptionData) {
  try {
    // Validate the data
    const validatedData = subscriptionSchema.parse(data);

    // Create a Supabase client
    const supabase = await createClient();

    // Get the subscription plan to verify it belongs to the correct tenant
    const { data: plan, error: planError } = await supabase
      .from("subscription_plans")
      .select("tenant_id")
      .eq("id", validatedData.plan_id)
      .single();

    if (planError || !plan) {
      throw new Error(
        `Failed to find subscription plan: ${
          planError?.message || "Subscription plan not found"
        }`
      );
    }

    // Verify that the member belongs to the same tenant as the plan
    const { data: tenantMember, error: tenantMemberError } = await supabase
      .from("tenant_members")
      .select("id")
      .eq("member_id", validatedData.member_id)
      .eq("tenant_id", plan.tenant_id)
      .single();

    if (tenantMemberError || !tenantMember) {
      throw new Error(
        `Member does not belong to the same tenant as the subscription plan. Please ensure the member is part of this tenant.`
      );
    }

    // Remove current_tenant_id from validatedData before inserting
    const { current_tenant_id, ...subscriptionData } = validatedData;

    // Store the tenant_id for revalidation
    const tenantId = plan.tenant_id;

    // Make sure start_date is not undefined
    if (!subscriptionData.start_date) {
      subscriptionData.start_date = new Date().toISOString();
    }

    // Insert the subscription
    const { data: subscription, error } = await supabase
      .from("subscriptions")
      .insert([subscriptionData as any])
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create subscription: ${error.message}`);
    }

    // Revalidate the subscriptions page to update the UI
    revalidatePath(`/tenant/${tenantId}/subscriptions`);
    revalidatePath(`/tenant/${tenantId}/members/${validatedData.member_id}`);

    return { success: true, data: subscription };
  } catch (error) {
    console.error("Error creating subscription:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to create subscription",
    };
  }
}

/**
 * Update an existing subscription
 */
export async function updateSubscription(
  id: string,
  data: Partial<SubscriptionData>
) {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Get the current subscription to get the member_id
    const { data: currentSubscription, error: fetchError } = await supabase
      .from("subscriptions")
      .select("member_id")
      .eq("id", id)
      .single();

    if (fetchError || !currentSubscription) {
      throw new Error(
        `Failed to find subscription: ${
          fetchError?.message || "Subscription not found"
        }`
      );
    }

    // Get the subscription details including plan_id
    const { data: subscriptionDetails, error: subscriptionError } =
      await supabase
        .from("subscriptions")
        .select("plan_id")
        .eq("id", id)
        .single();

    if (subscriptionError || !subscriptionDetails) {
      throw new Error(
        `Failed to get subscription details: ${
          subscriptionError?.message || "Subscription not found"
        }`
      );
    }

    // Get the tenant_id from the subscription plan
    const { data: plan, error: planError } = await supabase
      .from("subscription_plans")
      .select("tenant_id")
      .eq("id", subscriptionDetails.plan_id)
      .single();

    if (planError || !plan) {
      throw new Error(
        `Failed to find subscription plan: ${
          planError?.message || "Subscription plan not found"
        }`
      );
    }

    // Use the plan's tenant_id
    const tenantMember = { tenant_id: plan.tenant_id };

    // Update the subscription
    const { data: subscription, error } = await supabase
      .from("subscriptions")
      .update(data)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update subscription: ${error.message}`);
    }

    // Revalidate the subscription pages to update the UI
    revalidatePath(`/tenant/${tenantMember.tenant_id}/subscriptions`);
    revalidatePath(`/tenant/${tenantMember.tenant_id}/subscriptions/${id}`);
    revalidatePath(
      `/tenant/${tenantMember.tenant_id}/members/${currentSubscription.member_id}`
    );

    return { success: true, data: subscription };
  } catch (error) {
    console.error("Error updating subscription:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to update subscription",
    };
  }
}

/**
 * Cancel a subscription
 */
export async function cancelSubscription(id: string) {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Get the current subscription to get the member_id
    const { data: currentSubscription, error: fetchError } = await supabase
      .from("subscriptions")
      .select("member_id")
      .eq("id", id)
      .single();

    if (fetchError || !currentSubscription) {
      throw new Error(
        `Failed to find subscription: ${
          fetchError?.message || "Subscription not found"
        }`
      );
    }

    // Get the subscription details including plan_id
    const { data: subscriptionDetails, error: subscriptionError } =
      await supabase
        .from("subscriptions")
        .select("plan_id")
        .eq("id", id)
        .single();

    if (subscriptionError || !subscriptionDetails) {
      throw new Error(
        `Failed to get subscription details: ${
          subscriptionError?.message || "Subscription not found"
        }`
      );
    }

    // Get the tenant_id from the subscription plan
    const { data: plan, error: planError } = await supabase
      .from("subscription_plans")
      .select("tenant_id")
      .eq("id", subscriptionDetails.plan_id)
      .single();

    if (planError || !plan) {
      throw new Error(
        `Failed to find subscription plan: ${
          planError?.message || "Subscription plan not found"
        }`
      );
    }

    // Use the plan's tenant_id
    const tenantMember = { tenant_id: plan.tenant_id };

    // Update the subscription status to cancelled
    // Note: We use "cancelled" (British spelling) for consistency in the database
    const { data: subscription, error } = await supabase
      .from("subscriptions")
      .update({ status: "cancelled", auto_renew: false })
      .eq("id", id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to cancel subscription: ${error.message}`);
    }

    // Revalidate the subscription pages to update the UI
    revalidatePath(`/tenant/${tenantMember.tenant_id}/subscriptions`);
    revalidatePath(`/tenant/${tenantMember.tenant_id}/subscriptions/${id}`);
    revalidatePath(
      `/tenant/${tenantMember.tenant_id}/members/${currentSubscription.member_id}`
    );

    return { success: true, data: subscription };
  } catch (error) {
    console.error("Error cancelling subscription:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to cancel subscription",
    };
  }
}

/**
 * Get all subscriptions for a tenant
 * This function is cached to improve performance
 */
export const getSubscriptions = cache(async (tenantId: string) => {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Get all subscriptions for the tenant directly using the subscription_plans.tenant_id
    const { data, error } = await supabase
      .from("subscriptions")
      .select(
        `
        *,
        members(
          id,
          first_name,
          last_name,
          email
        ),
        subscription_plans!inner(
          id,
          name,
          price,
          billing_cycle,
          tenant_id
        )
      `
      )
      .eq("subscription_plans.tenant_id", tenantId)
      .order("created_at", { ascending: false });

    if (error) {
      throw new Error(`Failed to get subscriptions: ${error.message}`);
    }

    // Transform the data to match the expected format in the client
    const transformedData =
      data?.map((subscription) => {
        // Create a transformed subscription object
        return {
          ...subscription,
          members: {
            ...subscription.members,
            tenant_id: tenantId,
            status: "active", // Default status
          },
        };
      }) || [];

    return { success: true, data: transformedData };
  } catch (error) {
    console.error("Error getting subscriptions:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to get subscriptions",
      data: [],
    };
  }
});

/**
 * Get a subscription by ID
 * This function is cached to improve performance
 * @param id The subscription ID
 * @param tenantId Optional tenant ID to verify the subscription belongs to this tenant
 */
export const getSubscriptionById = cache(
  async (id: string, tenantId?: string) => {
    try {
      // Create a Supabase client
      const supabase = await createClient();

      // Build the query
      let query = supabase
        .from("subscriptions")
        .select(
          `
        *,
        members(
          id,
          first_name,
          last_name,
          email,
          tenant_members(
            tenant_id,
            status
          )
        ),
        subscription_plans(
          id,
          name,
          price,
          billing_cycle,
          tenant_id
        ),
        payments(*)
      `
        )
        .eq("id", id);

      // If tenantId is provided, filter by tenant_id in subscription_plans
      if (tenantId) {
        query = query.eq("subscription_plans.tenant_id", tenantId);
      }

      // Execute the query
      const { data, error } = await query.single();

      if (error) {
        throw new Error(`Failed to get subscription: ${error.message}`);
      }

      // Transform the data to match the expected format in the client
      if (data) {
        // Extract tenant_members data
        const tenantMember = data.members.tenant_members[0] || {};

        // Create a transformed subscription object
        const transformedData = {
          ...data,
          members: {
            ...data.members,
            tenant_id: tenantMember.tenant_id,
            status: tenantMember.status,
          },
        };

        return { success: true, data: transformedData };
      }

      return { success: true, data };
    } catch (error) {
      console.error("Error getting subscription:", error);
      return {
        success: false,
        error:
          error instanceof Error ? error.message : "Failed to get subscription",
        data: null,
      };
    }
  }
);

/**
 * Get all subscriptions for a member in a specific tenant
 * This function is cached to improve performance
 */
export const getMemberSubscriptions = cache(
  async (memberId: string, tenantId?: string) => {
    try {
      // Create a Supabase client
      const supabase = await createClient();

      // Build the query
      let query = supabase
        .from("subscriptions")
        .select(
          `
        *,
        subscription_plans!inner(*),
        members!inner(
          tenant_members(
            tenant_id,
            status
          )
        )
      `
        )
        .eq("member_id", memberId);

      // If tenantId is provided, filter by tenant_id in subscription_plans
      if (tenantId) {
        query = query.eq("subscription_plans.tenant_id", tenantId);
      }

      // Execute the query
      const { data, error } = await query.order("created_at", {
        ascending: false,
      });

      if (error) {
        throw new Error(`Failed to get member subscriptions: ${error.message}`);
      }

      // Transform the data to match the expected format in the client
      const transformedData =
        data?.map((subscription) => {
          // Extract tenant_members data
          const tenantMember = subscription.members.tenant_members[0] || {};

          // Create a transformed subscription object
          return {
            ...subscription,
            members: {
              ...subscription.members,
              tenant_id: tenantMember.tenant_id,
              status: tenantMember.status,
            },
          };
        }) || [];

      return { success: true, data: transformedData };
    } catch (error) {
      console.error("Error getting member subscriptions:", error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to get member subscriptions",
        data: [],
      };
    }
  }
);
