"use server";

import { createClient } from "@/utils/supabase/server";
import { cache } from "react";

/**
 * Get a tenant by slug for public access
 * This function is cached to improve performance
 */
export const getPublicTenant = cache(async (slug: string) => {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Get the tenant by slug
    // First try with public_page_enabled column
    let { data, error } = await supabase
      .from("tenants")
      .select("*")
      .eq("slug", slug)
      .single();

    // If the column doesn't exist or the value is false, return null
    if (error || !data || data.public_page_enabled === false) {
      return { success: false, error: "Public page not enabled", data: null };
    }

    if (error) {
      throw new Error(`Failed to get tenant: ${error.message}`);
    }

    return { success: true, data };
  } catch (error) {
    console.error("Error getting public tenant:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get tenant",
      data: null,
    };
  }
});

/**
 * Get all active subscription plans for a tenant for public access
 * This function is cached to improve performance
 */
export const getPublicPlans = cache(async (tenantId: string) => {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Get all active plans for the tenant
    const { data, error } = await supabase
      .from("subscription_plans")
      .select("*")
      .eq("tenant_id", tenantId)
      .eq("is_active", true)
      .order("price", { ascending: true });

    if (error) {
      throw new Error(`Failed to get plans: ${error.message}`);
    }

    return { success: true, data: data || [] };
  } catch (error) {
    console.error("Error getting public plans:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get plans",
      data: [],
    };
  }
});
