"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";
import { cache } from "react";
import { prisma } from "@/lib/prisma";

// Define the member login schema for validation
const memberLoginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

// Define the member password reset schema for validation
const memberPasswordResetSchema = z.object({
  email: z.string().email("Invalid email address"),
});

// Define the member password update schema for validation
const memberPasswordUpdateSchema = z
  .object({
    password: z.string().min(6, "Password must be at least 6 characters"),
    confirmPassword: z
      .string()
      .min(6, "Password must be at least 6 characters"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

/**
 * Get a member by email
 * This function is cached to improve performance
 */
export const getMemberByEmail = cache(async (email: string) => {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Get the member by email
    const { data, error } = await supabase
      .from("members")
      .select(
        `
        *,
        tenant_members(*)
      `
      )
      .eq("email", email)
      .single();

    if (error) {
      throw new Error(`Failed to get member: ${error.message}`);
    }

    return { success: true, data };
  } catch (error) {
    console.error("Error getting member by email:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get member",
      data: null,
    };
  }
});

/**
 * Get member by auth ID
 * This function is cached to improve performance
 */
export const getMemberByAuthId = cache(async (authId: string) => {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Get the member by auth ID
    const { data: member, error: memberError } = await supabase
      .from("members")
      .select("*")
      .eq("auth_id", authId)
      .single();

    if (memberError) {
      throw new Error(`Failed to get member: ${memberError.message}`);
    }

    // Get all tenant associations for this member
    const { data: tenantMembers, error: tenantsError } = await supabase
      .from("tenant_members")
      .select(
        `
        *,
        tenant:tenant_id(
          id,
          name,
          slug,
          logo_url
        )
      `
      )
      .eq("member_id", member.id);

    console.log("memberactionlog", member.id, tenantMembers, tenantsError);

    if (tenantsError) {
      throw new Error(
        `Failed to get tenant associations: ${tenantsError.message}`
      );
    }

    // Check if there are any tenant associations with null tenant data
    const missingTenantIds = tenantMembers
      .filter((tm) => tm.tenant === null)
      .map((tm) => tm.tenant_id);

    console.log("Missing tenant IDs:", missingTenantIds);

    // If there are missing tenants, fetch them directly
    let missingTenants = [];
    if (missingTenantIds.length > 0) {
      const { data: fetchedTenants, error: fetchError } = await supabase
        .from("tenants")
        .select("id, name, slug, logo_url")
        .in("id", missingTenantIds);

      if (!fetchError && fetchedTenants) {
        missingTenants = fetchedTenants;
        console.log("Fetched missing tenants:", missingTenants);
      }
    }

    // Create complete tenant members by combining the ones with tenant data
    // and the ones we had to fetch separately
    const completeTenantMembers = [
      ...tenantMembers.filter((tm) => tm.tenant !== null),
      ...tenantMembers
        .filter((tm) => tm.tenant === null)
        .map((tm) => {
          const fetchedTenant = missingTenants.find(
            (t) => t.id === tm.tenant_id
          );
          return {
            ...tm,
            tenant: fetchedTenant || null,
          };
        })
        .filter((tm) => tm.tenant !== null),
    ];

    console.log("Complete tenant members:", completeTenantMembers);

    // Extract all tenants from the complete tenant_members records
    const allTenants = completeTenantMembers.map((tm) => ({
      ...tm.tenant,
      status: tm.status,
      tenant_id: tm.tenant_id,
    }));

    // If there are no complete tenant associations, return an error
    if (completeTenantMembers.length === 0) {
      throw new Error("Member has no valid tenant associations");
    }

    // Use the first complete tenant as the default
    const defaultTenant = completeTenantMembers[0];

    // Combine the member with all tenants
    const enrichedMember = {
      ...member,
      tenant_id: defaultTenant.tenant_id,
      status: defaultTenant.status,
      tenants: defaultTenant.tenant,
      all_tenants: allTenants,
      all_memberships: completeTenantMembers,
    };

    return { success: true, data: enrichedMember };
  } catch (error) {
    console.error("Error getting member by auth ID:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get member",
      data: null,
    };
  }
});

/**
 * Create an auth account for a member
 */
export async function createMemberAuth(
  memberId: string,
  email: string,
  password: string
) {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Check if the member already has an auth account
    const { data: member, error: memberError } = await supabase
      .from("members")
      .select("*")
      .eq("id", memberId)
      .single();

    if (memberError) {
      throw new Error(`Failed to get member: ${memberError.message}`);
    }

    if (member.auth_id) {
      throw new Error("Member already has an auth account");
    }

    // Use our custom function to create the member auth
    const { data: result, error: fnError } = await supabase.rpc(
      "create_member_auth",
      {
        p_email: email,
        p_password: password,
        p_member_id: memberId,
      }
    );

    if (fnError) {
      throw new Error(`Failed to create member auth: ${fnError.message}`);
    }

    // Get the updated member
    const { data: updatedMember, error: getError } = await supabase
      .from("members")
      .select("*")
      .eq("id", memberId)
      .single();

    if (getError) {
      throw new Error(`Failed to get updated member: ${getError.message}`);
    }

    return { success: true, data: updatedMember };
  } catch (error) {
    console.error("Error creating member auth:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to create member auth",
    };
  }
}

/**
 * Send a password reset email to a member
 */
export async function sendMemberPasswordReset(email: string) {
  try {
    // Validate the email
    const validatedData = memberPasswordResetSchema.parse({ email });

    // Create a Supabase client
    const supabase = await createClient();

    // Send the password reset email
    const { error } = await supabase.auth.resetPasswordForEmail(
      validatedData.email,
      {
        redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/member/reset-password/callback`,
      }
    );

    if (error) {
      throw new Error(`Failed to send password reset email: ${error.message}`);
    }

    return { success: true };
  } catch (error) {
    console.error("Error sending password reset email:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to send password reset email",
    };
  }
}

/**
 * Update a member's password
 */
export async function updateMemberPassword(
  data: z.infer<typeof memberPasswordUpdateSchema>
) {
  try {
    // Validate the data
    const validatedData = memberPasswordUpdateSchema.parse(data);

    // Create a Supabase client
    const supabase = await createClient();

    // Update the password
    const { error } = await supabase.auth.updateUser({
      password: validatedData.password,
    });

    if (error) {
      throw new Error(`Failed to update password: ${error.message}`);
    }

    // Update the member's password_set flag
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError || !userData.user) {
      throw new Error("Authentication required");
    }

    const { error: updateError } = await supabase
      .from("members")
      .update({
        password_set: true,
      })
      .eq("auth_id", userData.user.id);

    if (updateError) {
      throw new Error(`Failed to update member: ${updateError.message}`);
    }

    return { success: true };
  } catch (error) {
    console.error("Error updating password:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to update password",
    };
  }
}
