import Link from "next/link";
import { redirect } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { getCurrentUser } from "@/lib/auth";

export default async function Home() {
  // Check if user is authenticated
  // const user = await getCurrentUser();

  // If authenticated, redirect to business selection
  // if (user) {
  //   redirect("/business/select");
  // }
  return (
    <div className="w-full min-h-screen flex-col">
      <header className="sticky top-0 z-40 border-b bg-background">
        <div className="container mx-auto flex h-16 items-center justify-between py-4">
          <div className="flex items-center gap-2">
            <span className="text-xl font-bold">GymSaaS</span>
          </div>
          <div className="flex items-center gap-4">
            <Button variant="ghost" asChild>
              <Link href="/auth/login">Business Login</Link>
            </Button>
            <Button variant="ghost" asChild>
              <Link href="/member/login">Member Login</Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/member/register">Member Register</Link>
            </Button>
            <Button asChild>
              <Link href="/auth/register">Business Signup</Link>
            </Button>
          </div>
        </div>
      </header>
      <main className="w-full">
        <section className="py-20 md:py-32">
          <div className="container mx-auto flex flex-col items-center text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl lg:text-7xl">
              Membership Management for Fitness Businesses
            </h1>
            <p className="mt-6 max-w-3xl text-lg text-muted-foreground sm:text-xl">
              A complete solution for gyms, yoga studios, and clubs to manage
              members, subscriptions, and payments in one place.
            </p>
            <div className="mt-10 flex flex-col gap-4 sm:flex-row">
              <Button size="lg" asChild>
                <Link href="/auth/register">Start Free Trial</Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="#features">Learn More</Link>
              </Button>
            </div>
          </div>
        </section>

        <section id="features" className="py-20 bg-muted/50">
          <div className="container mx-auto">
            <div className="mb-12 text-center">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl">
                Features
              </h2>
              <p className="mt-4 text-lg text-muted-foreground">
                Everything you need to run your fitness business
              </p>
            </div>
            <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
              <div className="rounded-lg border bg-card p-6 shadow-sm">
                <h3 className="text-xl font-bold">Member Management</h3>
                <p className="mt-2 text-muted-foreground">
                  Easily add, update, and track all your members in one place.
                </p>
              </div>
              <div className="rounded-lg border bg-card p-6 shadow-sm">
                <h3 className="text-xl font-bold">Subscription Plans</h3>
                <p className="mt-2 text-muted-foreground">
                  Create and manage different membership plans with flexible
                  pricing.
                </p>
              </div>
              <div className="rounded-lg border bg-card p-6 shadow-sm">
                <h3 className="text-xl font-bold">Payment Processing</h3>
                <p className="mt-2 text-muted-foreground">
                  Handle payments and track revenue with our simple payment
                  system.
                </p>
              </div>
              <div className="rounded-lg border bg-card p-6 shadow-sm">
                <h3 className="text-xl font-bold">Multi-tenant</h3>
                <p className="mt-2 text-muted-foreground">
                  Manage multiple locations or businesses from a single account.
                </p>
              </div>
              <div className="rounded-lg border bg-card p-6 shadow-sm">
                <h3 className="text-xl font-bold">Dashboard Analytics</h3>
                <p className="mt-2 text-muted-foreground">
                  Get insights into your business with our comprehensive
                  dashboard.
                </p>
              </div>
              <div className="rounded-lg border bg-card p-6 shadow-sm">
                <h3 className="text-xl font-bold">Role-based Access</h3>
                <p className="mt-2 text-muted-foreground">
                  Assign different roles to your staff with appropriate
                  permissions.
                </p>
              </div>
            </div>
          </div>
        </section>

        <section className="py-20">
          <div className="container mx-auto text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl">
              Ready to get started?
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Join thousands of fitness businesses already using GymSaaS
            </p>
            <div className="mt-10">
              <Button size="lg" asChild>
                <Link href="/auth/register">Sign Up Now</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      <footer className="border-t py-6 md:py-10">
        <div className="container mx-auto flex flex-col items-center justify-between gap-4 md:flex-row">
          <p className="text-sm text-muted-foreground">
            &copy; {new Date().getFullYear()} GymSaaS. All rights reserved.
          </p>
          <div className="flex gap-4">
            <Link
              href="#"
              className="text-sm text-muted-foreground hover:underline"
            >
              Terms
            </Link>
            <Link
              href="#"
              className="text-sm text-muted-foreground hover:underline"
            >
              Privacy
            </Link>
            <Link
              href="#"
              className="text-sm text-muted-foreground hover:underline"
            >
              Contact
            </Link>
          </div>
        </div>
      </footer>
    </div>
  );
}
