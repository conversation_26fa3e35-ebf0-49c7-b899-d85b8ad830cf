import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get("code");

  if (code) {
    try {
      const supabase = await createClient();
      await supabase.auth.exchangeCodeForSession(code);
      console.log("Successfully exchanged code for session");
    } catch (error) {
      console.error("Error exchanging code for session:", error);
    }
  }

  // URL to redirect to after sign in process completes
  return NextResponse.redirect(new URL("/business/select", request.url));
}
