import { notFound } from "next/navigation";
import { getPublicTenant, getPublicPlans } from "@/app/actions/public";
import PublicPlansClient from "./public-plans-client";

interface PublicPlansPageProps {
  params: {
    slug: string;
  };
}

export async function generateMetadata({ params }: PublicPlansPageProps) {
  const { slug } = params;
  const tenantResult = await getPublicTenant(slug);

  if (!tenantResult.success || !tenantResult.data) {
    return {
      title: "Plans Not Found",
      description: "The requested plans page could not be found.",
    };
  }

  const tenant = tenantResult.data;

  return {
    title: tenant.public_page_title || `${tenant.name} - Membership Plans`,
    description:
      tenant.public_page_description ||
      `View membership plans for ${tenant.name}`,
  };
}

export default async function PublicPlansPage({
  params,
}: PublicPlansPageProps) {
  const { slug } = params;
  const tenantResult = await getPublicTenant(slug);

  if (!tenantResult.success || !tenantResult.data) {
    notFound();
  }

  const tenant = tenantResult.data;
  const plansResult = await getPublicPlans(tenant.id);
  const plans = plansResult.success ? plansResult.data : [];

  return <PublicPlansClient tenant={tenant} plans={plans} />;
}
