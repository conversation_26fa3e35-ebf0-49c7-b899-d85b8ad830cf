"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { createTenant } from "@/app/actions/tenants";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { createTenantSchema } from "@/lib/validations";
import { generateSlug } from "@/lib/utils";

type CreateTenantFormValues = z.infer<typeof createTenantSchema>;

export default function CreateTenantPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<CreateTenantFormValues>({
    resolver: zodResolver(createTenantSchema),
    defaultValues: {
      name: "",
      slug: "",
    },
  });

  // Watch name field for debugging
  form.watch("name");

  // Auto-generate slug from name
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value;
    form.setValue("name", name);
    form.setValue("slug", generateSlug(name));
  };

  async function onSubmit(data: CreateTenantFormValues) {
    setIsLoading(true);

    try {
      // Call the server action to create the tenant
      const result = await createTenant(data);

      if (!result.success) {
        throw new Error(result.error);
      }

      toast.success("Business created successfully");
      router.push(`/tenant/${data.slug}`);
    } catch (error) {
      console.error("Error creating tenant:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to create business"
      );
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="flex min-h-screen items-center justify-center px-4 py-12">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">
            Create a Business
          </CardTitle>
          <CardDescription>
            Enter your business details to get started
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Business Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="My Gym"
                        disabled={isLoading}
                        {...field}
                        onChange={handleNameChange}
                      />
                    </FormControl>
                    <FormDescription>The name of your business</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="slug"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Business URL</FormLabel>
                    <FormControl>
                      <div className="flex items-center">
                        <span className="text-sm text-muted-foreground mr-2">
                          /tenant/
                        </span>
                        <Input
                          placeholder="my-gym"
                          disabled={isLoading}
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormDescription>
                      This will be used in the URL for your business
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? "Creating..." : "Create Business"}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
