import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth";
import { getUserTenants } from "@/lib/auth";
import NewMemberClient from "./new-member-client";
import { getTenantBySlug } from "@/app/actions/tenants";

interface NewMemberPageProps {
  params: {
    slug: string;
  };
}

export default async function NewMemberPage({ params }: NewMemberPageProps) {
  try {
    const { slug } = await params;
    const user = await getCurrentUser();

    if (!user) {
      redirect("/auth/login");
    }

    const tenantResult = await getTenantBySlug(slug);

    if (!tenantResult.success || !tenantResult.data) {
      redirect("/business/select");
    }

    const tenant = tenantResult.data;
    const tenants = await getUserTenants(user.id);

    return <NewMemberClient user={user} tenant={tenant} tenants={tenants} />;
  } catch (error) {
    console.error("Error in NewMemberPage:", error);
    redirect("/business/select");
  }
}
