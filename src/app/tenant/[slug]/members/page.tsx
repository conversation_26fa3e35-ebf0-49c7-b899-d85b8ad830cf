import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth";
import { getUserTenants } from "@/lib/auth";
import MembersClient from "./members-client";
import { getTenantBySlug } from "@/app/actions/tenants";
import { getMembers } from "@/app/actions/members";

interface MembersPageProps {
  params: {
    slug: string;
  };
}

export default async function MembersPage({ params }: MembersPageProps) {
  try {
    const { slug } = await params;
    const user = await getCurrentUser();

    if (!user) {
      redirect("/auth/login");
    }

    const tenantResult = await getTenantBySlug(slug);

    if (!tenantResult.success || !tenantResult.data) {
      redirect("/business/select");
    }

    const tenant = tenantResult.data;
    const tenants = await getUserTenants(user.id);
    const membersResult = await getMembers(tenant.id);
    const members = membersResult.success ? membersResult.data : [];

    return (
      <MembersClient
        user={user}
        tenant={tenant}
        tenants={tenants}
        members={members}
      />
    );
  } catch (error) {
    console.error("Error in MembersPage:", error);
    redirect("/business/select");
  }
}
