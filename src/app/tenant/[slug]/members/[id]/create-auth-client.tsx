"use client";

import { Key, ExternalLink } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface MemberLoginStatusProps {
  member: any;
}

export default function CreateAuthClient({ member }: MemberLoginStatusProps) {
  const hasLogin = !!member.auth_id;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant={hasLogin ? "outline" : "secondary"}
            className={
              hasLogin
                ? "bg-green-50 border-green-200 text-green-700 hover:bg-green-100 hover:text-green-800"
                : ""
            }
          >
            <Key className="mr-2 h-4 w-4" />
            {hasLogin ? "Login Enabled" : "Self-Registration Available"}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>
            {hasLogin
              ? "This member has set up their login credentials"
              : "Member can register at /member/register using their email"}
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
