import { Skeleton } from "@/components/ui/skeleton";

export default function MemberDetailLoading() {
  return (
    <div className="flex min-h-screen flex-col">
      {/* Header skeleton */}
      <div className="sticky top-0 z-40 w-full border-b bg-background">
        <div className="container mx-auto flex h-16 items-center justify-between px-4 py-4 md:px-6">
          <div className="flex items-center gap-4">
            <Skeleton className="h-8 w-8 rounded-full" />
            <Skeleton className="h-6 w-32" />
          </div>
          <div className="flex items-center gap-4">
            <Skeleton className="h-8 w-8 rounded-full" />
          </div>
        </div>
      </div>
      
      <div className="flex flex-1 w-full">
        {/* Sidebar skeleton */}
        <aside className="hidden h-full w-64 flex-col border-r bg-background md:flex">
          <div className="flex flex-col gap-2 p-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <Skeleton key={i} className="h-10 w-full" />
            ))}
          </div>
        </aside>
        
        <main className="flex-1 overflow-y-auto">
          <div className="container mx-auto px-4 py-6 md:px-6">
            <div className="mb-6">
              <Skeleton className="h-10 w-24 mb-4" />
              <div className="flex items-center justify-between">
                <Skeleton className="h-8 w-48" />
                <div className="flex space-x-2">
                  <Skeleton className="h-10 w-24" />
                  <Skeleton className="h-10 w-24" />
                </div>
              </div>
            </div>
            
            <div className="grid gap-6 md:grid-cols-2">
              <div className="space-y-6">
                <div className="rounded-lg border p-6">
                  <h2 className="text-xl font-semibold mb-4">
                    <Skeleton className="h-6 w-32" />
                  </h2>
                  <div className="space-y-4">
                    <div>
                      <Skeleton className="h-4 w-24 mb-1" />
                      <Skeleton className="h-6 w-full" />
                    </div>
                    <div>
                      <Skeleton className="h-4 w-24 mb-1" />
                      <Skeleton className="h-6 w-full" />
                    </div>
                    <div>
                      <Skeleton className="h-4 w-24 mb-1" />
                      <Skeleton className="h-6 w-full" />
                    </div>
                    <div>
                      <Skeleton className="h-4 w-24 mb-1" />
                      <Skeleton className="h-6 w-full" />
                    </div>
                  </div>
                </div>
                
                <div className="rounded-lg border p-6">
                  <h2 className="text-xl font-semibold mb-4">
                    <Skeleton className="h-6 w-32" />
                  </h2>
                  <div className="space-y-4">
                    <Skeleton className="h-20 w-full" />
                  </div>
                </div>
              </div>
              
              <div className="space-y-6">
                <div className="rounded-lg border p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-xl font-semibold">
                      <Skeleton className="h-6 w-40" />
                    </h2>
                    <Skeleton className="h-9 w-32" />
                  </div>
                  
                  {[1, 2].map((i) => (
                    <div key={i} className="mb-4 rounded-lg border p-4">
                      <div className="flex items-center justify-between mb-2">
                        <Skeleton className="h-5 w-32" />
                        <Skeleton className="h-5 w-24" />
                      </div>
                      <div className="flex items-center justify-between">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-6 w-20 rounded-full" />
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="rounded-lg border p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-xl font-semibold">
                      <Skeleton className="h-6 w-32" />
                    </h2>
                  </div>
                  
                  {[1, 2].map((i) => (
                    <div key={i} className="mb-4 rounded-lg border p-4">
                      <div className="flex items-center justify-between mb-2">
                        <Skeleton className="h-5 w-32" />
                        <Skeleton className="h-5 w-24" />
                      </div>
                      <div className="flex items-center justify-between">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-4 w-20" />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
