import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth";
import { getUserTenants } from "@/lib/auth";
import MemberDetailClient from "./member-detail-client";
import { getTenantBySlug } from "@/app/actions/tenants";
import { getMemberById } from "@/app/actions/members";
import { getMemberSubscriptions } from "@/app/actions/subscriptions";

interface MemberDetailPageProps {
  params: {
    slug: string;
    id: string;
  };
}

export default async function MemberDetailPage({
  params,
}: MemberDetailPageProps) {
  try {
    const { slug, id } = await params;
    const user = await getCurrentUser();

    if (!user) {
      redirect("/auth/login");
    }

    const tenantResult = await getTenantBySlug(slug);

    if (!tenantResult.success || !tenantResult.data) {
      redirect("/business/select");
    }

    const tenant = tenantResult.data;
    const tenants = await getUserTenants(user.id);
    const memberResult = await getMemberById(id, tenant.id);

    if (!memberResult.success || !memberResult.data) {
      redirect(`/tenant/${slug}/members`);
    }

    const member = memberResult.data;
    // Pass the tenant ID to only get subscriptions for this tenant
    const subscriptionsResult = await getMemberSubscriptions(id, tenant.id);
    const subscriptions = subscriptionsResult.success
      ? subscriptionsResult.data
      : [];

    return (
      <MemberDetailClient
        user={user}
        tenant={tenant}
        tenants={tenants}
        member={member}
        subscriptions={subscriptions}
      />
    );
  } catch (error) {
    console.error("Error in MemberDetailPage:", error);
    redirect(`/tenant/${params.slug}/members`);
  }
}
