"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { formatDate, formatCurrency } from "@/lib/utils";
import { ArrowLeft, Edit, Trash, PlusCircle, Key } from "lucide-react";
import { toast } from "sonner";
import { deleteMember } from "@/app/actions/members";
import CreateAuthClient from "./create-auth-client";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { DashboardHeader } from "@/components/layout/dashboard-header";
import { DashboardSidebar } from "@/components/layout/dashboard-sidebar";

interface MemberDetailClientProps {
  user: any;
  tenant: any;
  tenants: any[];
  member: any;
  subscriptions: any[];
}

export default function MemberDetailClient({
  user,
  tenant,
  tenants,
  member,
  subscriptions,
}: MemberDetailClientProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-500";
      case "inactive":
        return "bg-gray-500";
      case "pending":
        return "bg-yellow-500";
      default:
        return "bg-gray-500";
    }
  };

  // Handle member deletion
  const handleDeleteMember = async () => {
    setIsLoading(true);
    try {
      // Call the server action to delete the member
      const result = await deleteMember(member.id, tenant.id);

      if (!result.success) {
        throw new Error(result.error);
      }

      toast.success("Member deleted successfully");
      router.push(`/tenant/${tenant.slug}/members`);
    } catch (error) {
      console.error("Error deleting member:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to delete member"
      );
    } finally {
      setIsLoading(false);
      setIsDeleteDialogOpen(false);
    }
  };

  return (
    <div className="flex min-h-screen flex-col">
      <DashboardHeader user={user} tenants={tenants} currentTenant={tenant} />
      <div className="flex flex-1 w-full">
        <DashboardSidebar tenantSlug={tenant.slug} />
        <main className="flex-1 overflow-y-auto">
          <div className="container mx-auto px-4 py-6 md:px-6">
            <div className="mb-6">
              <Button
                variant="ghost"
                onClick={() => router.push(`/tenant/${tenant.slug}/members`)}
                className="mb-4"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Members
              </Button>
              <div className="flex items-center justify-between">
                <h1 className="text-3xl font-bold">
                  {member.first_name} {member.last_name}
                </h1>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    onClick={() =>
                      router.push(
                        `/tenant/${tenant.slug}/members/${member.id}/edit`
                      )
                    }
                  >
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </Button>
                  <CreateAuthClient member={member} />
                  <Dialog
                    open={isDeleteDialogOpen}
                    onOpenChange={setIsDeleteDialogOpen}
                  >
                    <DialogTrigger asChild>
                      <Button variant="destructive">
                        <Trash className="mr-2 h-4 w-4" />
                        Delete
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Delete Member</DialogTitle>
                        <DialogDescription>
                          Are you sure you want to delete this member? This
                          action cannot be undone.
                        </DialogDescription>
                      </DialogHeader>
                      <DialogFooter>
                        <Button
                          variant="outline"
                          onClick={() => setIsDeleteDialogOpen(false)}
                        >
                          Cancel
                        </Button>
                        <Button
                          variant="destructive"
                          onClick={handleDeleteMember}
                          disabled={isLoading}
                        >
                          {isLoading ? "Deleting..." : "Delete"}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
            </div>

            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Member Information</CardTitle>
                  <CardDescription>Personal details and status</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Status
                      </p>
                      <Badge
                        variant="outline"
                        className={`${getStatusColor(
                          member.status
                        )} text-white mt-1`}
                      >
                        {member.status.charAt(0).toUpperCase() +
                          member.status.slice(1)}
                      </Badge>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Member Since
                      </p>
                      <p className="mt-1">{formatDate(member.created_at)}</p>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Member Login
                    </p>
                    <Badge
                      variant="outline"
                      className={`${
                        member.auth_id ? "bg-green-500" : "bg-gray-500"
                      } text-white mt-1`}
                    >
                      {member.auth_id ? "Enabled" : "Disabled"}
                    </Badge>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Email
                    </p>
                    <p className="mt-1">{member.email}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Phone
                    </p>
                    <p className="mt-1">{member.phone || "Not provided"}</p>
                  </div>
                  {member.notes && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Notes
                      </p>
                      <p className="mt-1">{member.notes}</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle>Subscriptions</CardTitle>
                    <CardDescription>
                      Active and past subscription plans
                    </CardDescription>
                  </div>
                  <Button asChild>
                    <a
                      href={`/tenant/${tenant.slug}/subscriptions/new?member=${member.id}`}
                    >
                      <PlusCircle className="mr-2 h-4 w-4" />
                      Add Subscription
                    </a>
                  </Button>
                </CardHeader>
                <CardContent>
                  {subscriptions.length > 0 ? (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Plan</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Start Date</TableHead>
                          <TableHead>End Date</TableHead>
                          <TableHead className="text-right">Price</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {subscriptions.map((subscription) => (
                          <TableRow key={subscription.id}>
                            <TableCell className="font-medium">
                              {subscription.subscription_plans.name}
                            </TableCell>
                            <TableCell>
                              <Badge
                                variant="outline"
                                className={`${getStatusColor(
                                  subscription.status
                                )} text-white`}
                              >
                                {subscription.status.charAt(0).toUpperCase() +
                                  subscription.status.slice(1)}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              {formatDate(subscription.start_date)}
                            </TableCell>
                            <TableCell>
                              {subscription.end_date
                                ? formatDate(subscription.end_date)
                                : "N/A"}
                            </TableCell>
                            <TableCell className="text-right">
                              {formatCurrency(
                                subscription.subscription_plans.price
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className="text-center py-6">
                      <p className="text-muted-foreground">
                        No subscriptions found for this member.
                      </p>
                      <Button className="mt-4" asChild>
                        <a
                          href={`/tenant/${tenant.slug}/subscriptions/new?member=${member.id}`}
                        >
                          Add First Subscription
                        </a>
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
