import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth";
import { getUserTenants } from "@/lib/auth";
import PublicPageSettingsClient from "./public-page-settings-client";
import { getTenantBySlug } from "@/app/actions/tenants";

interface PublicPageSettingsPageProps {
  params: {
    slug: string;
  };
}

export default async function PublicPageSettingsPage({
  params,
}: PublicPageSettingsPageProps) {
  try {
    const { slug } = await params;
    const user = await getCurrentUser();

    if (!user) {
      redirect("/auth/login");
    }

    const tenantResult = await getTenantBySlug(slug);

    if (!tenantResult.success || !tenantResult.data) {
      redirect("/business/select");
    }

    const tenant = tenantResult.data;
    const tenants = await getUserTenants(user.id);

    return (
      <PublicPageSettingsClient
        user={user}
        tenant={tenant}
        tenants={tenants}
      />
    );
  } catch (error) {
    console.error("Error in PublicPageSettingsPage:", error);
    redirect("/business/select");
  }
}
