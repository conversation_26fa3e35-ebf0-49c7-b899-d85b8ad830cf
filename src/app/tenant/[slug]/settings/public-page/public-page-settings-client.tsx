"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import { ExternalLink } from "lucide-react";
import { updatePublicPageSettings } from "@/app/actions/tenants";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { DashboardHeader } from "@/components/layout/dashboard-header";
import { DashboardSidebar } from "@/components/layout/dashboard-sidebar";

// Define the form schema
const publicPageSchema = z.object({
  public_page_enabled: z.boolean().default(false),
  public_page_title: z.string().optional(),
  public_page_description: z.string().optional(),
});

type PublicPageFormValues = z.infer<typeof publicPageSchema>;

interface PublicPageSettingsClientProps {
  user: any;
  tenant: any;
  tenants: any[];
}

export default function PublicPageSettingsClient({
  user,
  tenant,
  tenants,
}: PublicPageSettingsClientProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  // Initialize form with tenant values
  const form = useForm<PublicPageFormValues>({
    resolver: zodResolver(publicPageSchema),
    defaultValues: {
      public_page_enabled: tenant.public_page_enabled || false,
      public_page_title: tenant.public_page_title || "",
      public_page_description: tenant.public_page_description || "",
    },
  });

  // Get the public page URL
  const publicPageUrl = `${window.location.origin}/p/${tenant.slug}`;

  async function onSubmit(data: PublicPageFormValues) {
    setIsLoading(true);

    try {
      const result = await updatePublicPageSettings(tenant.id, {
        public_page_enabled: data.public_page_enabled,
        public_page_title: data.public_page_title,
        public_page_description: data.public_page_description,
      });

      if (!result.success) {
        throw new Error(result.error);
      }

      toast.success("Public page settings updated successfully");
      router.refresh();
    } catch (error) {
      console.error("Error updating public page settings:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to update public page settings"
      );
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="flex min-h-screen flex-col">
      <DashboardHeader user={user} tenants={tenants} currentTenant={tenant} />
      <div className="flex flex-1 w-full">
        <DashboardSidebar tenantSlug={tenant.slug} />
        <main className="flex-1 overflow-y-auto">
          <div className="container mx-auto px-4 py-6 md:px-6">
            <div className="mb-6">
              <h1 className="text-3xl font-bold">Public Page Settings</h1>
              <p className="text-muted-foreground">
                Configure your public membership plans page
              </p>
            </div>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Public Page Configuration</CardTitle>
                  <CardDescription>
                    Enable and customize your public membership plans page
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Form {...form}>
                    <form
                      onSubmit={form.handleSubmit(onSubmit)}
                      className="space-y-6"
                    >
                      <FormField
                        control={form.control}
                        name="public_page_enabled"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">
                                Enable Public Page
                              </FormLabel>
                              <FormDescription>
                                Make your membership plans visible to the public
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                disabled={isLoading}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="public_page_title"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Page Title</FormLabel>
                            <FormControl>
                              <Input
                                placeholder={`${tenant.name} - Membership Plans`}
                                {...field}
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormDescription>
                              The title displayed on your public page
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="public_page_description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Page Description</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder={`Choose the perfect membership plan for you at ${tenant.name}`}
                                {...field}
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormDescription>
                              A brief description of your membership plans
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="flex justify-end">
                        <Button type="submit" disabled={isLoading}>
                          {isLoading ? "Saving..." : "Save Settings"}
                        </Button>
                      </div>
                    </form>
                  </Form>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Public Page Information</CardTitle>
                  <CardDescription>
                    Details about your public membership plans page
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h3 className="font-medium">Public URL</h3>
                    <div className="flex items-center mt-1">
                      <code className="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">
                        {publicPageUrl}
                      </code>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ml-2"
                        onClick={() => {
                          navigator.clipboard.writeText(publicPageUrl);
                          toast.success("URL copied to clipboard");
                        }}
                      >
                        Copy
                      </Button>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-medium">Visible Plans</h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      Only active plans will be visible on your public page
                    </p>
                  </div>

                  <div>
                    <h3 className="font-medium">Contact Information</h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      Visitors will be directed to contact you for signup
                    </p>
                  </div>
                </CardContent>
                <CardFooter>
                  {tenant.public_page_enabled && (
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => window.open(publicPageUrl, "_blank")}
                    >
                      <ExternalLink className="mr-2 h-4 w-4" />
                      View Public Page
                    </Button>
                  )}
                </CardFooter>
              </Card>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
