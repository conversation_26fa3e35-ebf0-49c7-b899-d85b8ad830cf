import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth";
import { getUserTenants } from "@/lib/auth";
import PaymentsClient from "./payments-client";
import { getTenantBySlug } from "@/app/actions/tenants";
import { getPayments } from "@/app/actions/payments";

interface PaymentsPageProps {
  params: {
    slug: string;
  };
}

export default async function PaymentsPage({ params }: PaymentsPageProps) {
  try {
    const { slug } = await params;
    const user = await getCurrentUser();

    if (!user) {
      redirect("/auth/login");
    }

    const tenantResult = await getTenantBySlug(slug);

    if (!tenantResult.success || !tenantResult.data) {
      redirect("/business/select");
    }

    const tenant = tenantResult.data;
    const tenants = await getUserTenants(user.id);
    const paymentsResult = await getPayments(tenant.id);

    // Process the payments data to handle nested arrays
    let payments = [];
    if (paymentsResult.success && paymentsResult.data) {
      payments = paymentsResult.data.map((payment) => {
        // For each payment, if subscriptions is an array, take the first item
        return {
          ...payment,
          subscriptions:
            payment.subscriptions &&
            Array.isArray(payment.subscriptions) &&
            payment.subscriptions.length > 0
              ? payment.subscriptions[0]
              : payment.subscriptions || {},
        };
      });
    }

    return (
      <PaymentsClient
        user={user}
        tenant={tenant}
        tenants={tenants}
        payments={payments}
      />
    );
  } catch (error) {
    console.error("Error in PaymentsPage:", error);
    redirect("/business/select");
  }
}
