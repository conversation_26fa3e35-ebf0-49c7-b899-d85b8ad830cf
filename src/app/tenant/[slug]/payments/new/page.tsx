import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth";
import { getUserTenants } from "@/lib/auth";
import NewPaymentClient from "./new-payment-client";
import { getTenantBySlug } from "@/app/actions/tenants";
import { getSubscriptions } from "@/app/actions/subscriptions";

interface NewPaymentPageProps {
  params: {
    slug: string;
  };
  searchParams: {
    subscription_id?: string;
  };
}

export default async function NewPaymentPage({
  params,
  searchParams,
}: NewPaymentPageProps) {
  try {
    const { slug } = await params;
    const { subscription_id } = searchParams;
    const user = await getCurrentUser();

    if (!user) {
      redirect("/auth/login");
    }

    const tenantResult = await getTenantBySlug(slug);

    if (!tenantResult.success || !tenantResult.data) {
      redirect("/business/select");
    }

    const tenant = tenantResult.data;
    const tenants = await getUserTenants(user.id);
    
    // Get all subscriptions for the tenant
    const subscriptionsResult = await getSubscriptions(tenant.id);
    const subscriptions = subscriptionsResult.success
      ? subscriptionsResult.data
      : [];

    return (
      <NewPaymentClient
        user={user}
        tenant={tenant}
        tenants={tenants}
        subscriptions={subscriptions}
        preselectedSubscriptionId={subscription_id}
      />
    );
  } catch (error) {
    console.error("Error in NewPaymentPage:", error);
    redirect("/business/select");
  }
}
