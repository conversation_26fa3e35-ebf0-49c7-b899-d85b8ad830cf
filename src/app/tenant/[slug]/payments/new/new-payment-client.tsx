"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { format } from "date-fns";
import { z } from "zod";
import { paymentSchema } from "@/lib/validations";
import { formatCurrency } from "@/lib/utils";
import { createPayment } from "@/app/actions/payments";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { DashboardHeader } from "@/components/layout/dashboard-header";
import { DashboardSidebar } from "@/components/layout/dashboard-sidebar";

type NewPaymentFormValues = z.infer<typeof paymentSchema>;

interface NewPaymentClientProps {
  user: any;
  tenant: any;
  tenants: any[];
  subscriptions: any[];
  preselectedSubscriptionId?: string;
}

export default function NewPaymentClient({
  user,
  tenant,
  tenants,
  subscriptions,
  preselectedSubscriptionId,
}: NewPaymentClientProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [selectedSubscription, setSelectedSubscription] = useState<any>(null);

  // Initialize form with default values
  const form = useForm<NewPaymentFormValues>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      subscription_id: preselectedSubscriptionId || "",
      amount: 0,
      payment_method: "credit_card",
    },
  });

  // Watch subscription_id to update selected subscription
  const watchSubscriptionId = form.watch("subscription_id");

  // Update selected subscription when subscription_id changes
  useState(() => {
    if (watchSubscriptionId) {
      const subscription = subscriptions.find(
        (sub) => sub.id === watchSubscriptionId
      );
      if (subscription) {
        setSelectedSubscription(subscription);
        // Set default amount to plan price
        form.setValue("amount", subscription.subscription_plans.price);
      }
    }
  });

  async function onSubmit(data: NewPaymentFormValues) {
    setIsLoading(true);

    try {
      // Add additional required fields
      const paymentData = {
        ...data,
        status: "pending" as const,
        payment_date: new Date().toISOString(),
        transaction_id: `txn_${Math.random().toString(36).substring(2, 10)}`,
      };

      const result = await createPayment(paymentData);

      if (!result.success) {
        throw new Error(result.error);
      }

      toast.success("Payment created successfully");
      router.push(`/tenant/${tenant.slug}/payments`);
    } catch (error) {
      console.error("Error creating payment:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to create payment"
      );
    } finally {
      setIsLoading(false);
    }
  }

  // Get active subscriptions only
  const activeSubscriptions = subscriptions.filter(
    (sub) => sub.status === "active"
  );

  return (
    <div className="flex min-h-screen flex-col">
      <DashboardHeader user={user} tenants={tenants} currentTenant={tenant} />
      <div className="flex flex-1 w-full">
        <DashboardSidebar tenantSlug={tenant.slug} />
        <main className="flex-1 overflow-y-auto">
          <div className="container mx-auto px-4 py-6 md:px-6">
            <div className="mb-6">
              <h1 className="text-3xl font-bold">Add Payment</h1>
              <p className="text-muted-foreground">
                Create a new payment transaction
              </p>
            </div>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Payment Details</CardTitle>
                  <CardDescription>
                    Enter the payment information
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Form {...form}>
                    <form
                      onSubmit={form.handleSubmit(onSubmit)}
                      className="space-y-4"
                    >
                      <FormField
                        control={form.control}
                        name="subscription_id"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Subscription</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                              disabled={isLoading}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select a subscription" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {activeSubscriptions.length > 0 ? (
                                  activeSubscriptions.map((subscription) => (
                                    <SelectItem
                                      key={subscription.id}
                                      value={subscription.id}
                                    >
                                      {subscription.members.first_name}{" "}
                                      {subscription.members.last_name} -{" "}
                                      {subscription.subscription_plans.name} (
                                      {formatCurrency(
                                        subscription.subscription_plans.price
                                      )}
                                      )
                                    </SelectItem>
                                  ))
                                ) : (
                                  <SelectItem value="none" disabled>
                                    No active subscriptions found
                                  </SelectItem>
                                )}
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              Select the subscription this payment is for
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="amount"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Amount</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                step="0.01"
                                placeholder="0.00"
                                {...field}
                                onChange={(e) =>
                                  field.onChange(parseFloat(e.target.value))
                                }
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormDescription>
                              Enter the payment amount
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="payment_method"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Payment Method</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                              disabled={isLoading}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select a payment method" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="credit_card">
                                  Credit Card
                                </SelectItem>
                                <SelectItem value="debit_card">
                                  Debit Card
                                </SelectItem>
                                <SelectItem value="bank_transfer">
                                  Bank Transfer
                                </SelectItem>
                                <SelectItem value="cash">Cash</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              Select the payment method used
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="flex justify-end space-x-2 pt-4">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => router.back()}
                          disabled={isLoading}
                        >
                          Cancel
                        </Button>
                        <Button type="submit" disabled={isLoading}>
                          {isLoading ? "Creating..." : "Create Payment"}
                        </Button>
                      </div>
                    </form>
                  </Form>
                </CardContent>
              </Card>

              {selectedSubscription && (
                <Card>
                  <CardHeader>
                    <CardTitle>Subscription Information</CardTitle>
                    <CardDescription>
                      Details about the selected subscription
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h3 className="font-medium">Member</h3>
                      <p>
                        {selectedSubscription.members.first_name}{" "}
                        {selectedSubscription.members.last_name}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {selectedSubscription.members.email}
                      </p>
                    </div>
                    <div>
                      <h3 className="font-medium">Plan</h3>
                      <p>{selectedSubscription.subscription_plans.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {formatCurrency(
                          selectedSubscription.subscription_plans.price
                        )}{" "}
                        per{" "}
                        {selectedSubscription.subscription_plans.billing_cycle}
                      </p>
                    </div>
                    <div>
                      <h3 className="font-medium">Status</h3>
                      <p>
                        {selectedSubscription.status.charAt(0).toUpperCase() +
                          selectedSubscription.status.slice(1)}
                      </p>
                    </div>
                    <div>
                      <h3 className="font-medium">Start Date</h3>
                      <p>
                        {format(
                          new Date(selectedSubscription.start_date),
                          "PPP"
                        )}
                      </p>
                    </div>
                    {selectedSubscription.end_date && (
                      <div>
                        <h3 className="font-medium">End Date</h3>
                        <p>
                          {format(
                            new Date(selectedSubscription.end_date),
                            "PPP"
                          )}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
