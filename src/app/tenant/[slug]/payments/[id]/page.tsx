import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth";
import { getUserTenants } from "@/lib/auth";
import PaymentDetailClient from "./payment-detail-client";
import { getTenantBySlug } from "@/app/actions/tenants";
import { getPaymentById } from "@/app/actions/payments";

interface PaymentDetailPageProps {
  params: {
    slug: string;
    id: string;
  };
}

export default async function PaymentDetailPage({
  params,
}: PaymentDetailPageProps) {
  try {
    const { slug, id } = await params;
    const user = await getCurrentUser();

    if (!user) {
      redirect("/auth/login");
    }

    const tenantResult = await getTenantBySlug(slug);

    if (!tenantResult.success || !tenantResult.data) {
      redirect("/business/select");
    }

    const tenant = tenantResult.data;
    const tenants = await getUserTenants(user.id);

    // Get the payment details
    const paymentResult = await getPaymentById(id);

    if (!paymentResult.success || !paymentResult.data) {
      redirect(`/tenant/${slug}/payments`);
    }

    const payment = paymentResult.data;

    // Log the payment data structure to understand the issue
    console.log("Payment data structure:", JSON.stringify(payment, null, 2));

    // Prepare the payment data with properly structured subscription info
    const preparedPayment = {
      ...payment,
      // If subscriptions is an array, take the first item
      subscriptions:
        payment.subscriptions &&
        Array.isArray(payment.subscriptions) &&
        payment.subscriptions.length > 0
          ? payment.subscriptions[0]
          : payment.subscriptions || {},
    };

    return (
      <PaymentDetailClient
        user={user}
        tenant={tenant}
        tenants={tenants}
        payment={preparedPayment}
      />
    );
  } catch (error) {
    console.error("Error in PaymentDetailPage:", error);
    redirect("/business/select");
  }
}
