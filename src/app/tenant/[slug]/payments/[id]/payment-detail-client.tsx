"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { format } from "date-fns";
import { z } from "zod";
import { formatCurrency, formatDate } from "@/lib/utils";
import { updatePayment } from "@/app/actions/payments";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { DashboardHeader } from "@/components/layout/dashboard-header";
import { DashboardSidebar } from "@/components/layout/dashboard-sidebar";
import { Separator } from "@/components/ui/separator";

// Define the update payment schema
const updatePaymentSchema = z.object({
  status: z.enum(["pending", "succeeded", "failed", "refunded"]),
  payment_method: z.enum([
    "credit_card",
    "debit_card",
    "bank_transfer",
    "cash",
  ]),
});

type UpdatePaymentFormValues = z.infer<typeof updatePaymentSchema>;

interface PaymentDetailClientProps {
  user: any;
  tenant: any;
  tenants: any[];
  payment: any;
}

export default function PaymentDetailClient({
  user,
  tenant,
  tenants,
  payment,
}: PaymentDetailClientProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false);

  // Initialize form with payment values
  const form = useForm<UpdatePaymentFormValues>({
    resolver: zodResolver(updatePaymentSchema),
    defaultValues: {
      status: payment.status,
      payment_method: payment.payment_method,
    },
  });

  async function onSubmit(data: UpdatePaymentFormValues) {
    setIsLoading(true);

    try {
      const result = await updatePayment(payment.id, data);

      if (!result.success) {
        throw new Error(result.error);
      }

      toast.success("Payment updated successfully");
      setIsUpdateDialogOpen(false);
      router.refresh();
    } catch (error) {
      console.error("Error updating payment:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to update payment"
      );
    } finally {
      setIsLoading(false);
    }
  }

  // Get payment status color
  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case "succeeded":
        return "bg-green-500";
      case "pending":
        return "bg-yellow-500";
      case "failed":
        return "bg-red-500";
      case "refunded":
        return "bg-blue-500";
      default:
        return "bg-gray-500";
    }
  };

  // Get payment method display
  const getPaymentMethodDisplay = (method: string) => {
    switch (method) {
      case "credit_card":
        return "Credit Card";
      case "debit_card":
        return "Debit Card";
      case "bank_transfer":
        return "Bank Transfer";
      case "cash":
        return "Cash";
      default:
        return method;
    }
  };

  return (
    <div className="flex min-h-screen flex-col">
      <DashboardHeader user={user} tenants={tenants} currentTenant={tenant} />
      <div className="flex flex-1 w-full">
        <DashboardSidebar tenantSlug={tenant.slug} />
        <main className="flex-1 overflow-y-auto">
          <div className="container mx-auto px-4 py-6 md:px-6">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-3xl font-bold">Payment Details</h1>
                <p className="text-muted-foreground">
                  View and manage payment information
                </p>
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={() => router.push(`/tenant/${tenant.slug}/payments`)}
                >
                  Back to Payments
                </Button>
                <Dialog
                  open={isUpdateDialogOpen}
                  onOpenChange={setIsUpdateDialogOpen}
                >
                  <DialogTrigger asChild>
                    <Button>Update Status</Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Update Payment</DialogTitle>
                      <DialogDescription>
                        Change the status or payment method
                      </DialogDescription>
                    </DialogHeader>
                    <Form {...form}>
                      <form
                        onSubmit={form.handleSubmit(onSubmit)}
                        className="space-y-4"
                      >
                        <FormField
                          control={form.control}
                          name="status"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Status</FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                                disabled={isLoading}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select status" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="pending">
                                    Pending
                                  </SelectItem>
                                  <SelectItem value="succeeded">
                                    Succeeded
                                  </SelectItem>
                                  <SelectItem value="failed">Failed</SelectItem>
                                  <SelectItem value="refunded">
                                    Refunded
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="payment_method"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Payment Method</FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                                disabled={isLoading}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select payment method" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="credit_card">
                                    Credit Card
                                  </SelectItem>
                                  <SelectItem value="debit_card">
                                    Debit Card
                                  </SelectItem>
                                  <SelectItem value="bank_transfer">
                                    Bank Transfer
                                  </SelectItem>
                                  <SelectItem value="cash">Cash</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <DialogFooter>
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => setIsUpdateDialogOpen(false)}
                            disabled={isLoading}
                          >
                            Cancel
                          </Button>
                          <Button type="submit" disabled={isLoading}>
                            {isLoading ? "Updating..." : "Update Payment"}
                          </Button>
                        </DialogFooter>
                      </form>
                    </Form>
                  </DialogContent>
                </Dialog>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Payment Information</CardTitle>
                  <CardDescription>
                    Details about this payment transaction
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Amount</span>
                    <span className="text-xl font-bold">
                      {formatCurrency(payment.amount || 0)}
                    </span>
                  </div>
                  <Separator />
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Status</span>
                    <Badge
                      variant="outline"
                      className={`${
                        payment.status
                          ? getPaymentStatusColor(payment.status)
                          : "bg-gray-500"
                      } text-white`}
                    >
                      {payment.status
                        ? `${payment.status
                            .charAt(0)
                            .toUpperCase()}${payment.status.slice(1)}`
                        : "Unknown"}
                    </Badge>
                  </div>
                  <Separator />
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Payment Method</span>
                    <span>
                      {payment.payment_method
                        ? getPaymentMethodDisplay(payment.payment_method)
                        : "Unknown"}
                    </span>
                  </div>
                  <Separator />
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Payment Date</span>
                    <span>
                      {payment.payment_date
                        ? formatDate(payment.payment_date)
                        : "Date not available"}
                    </span>
                  </div>
                  <Separator />
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Transaction ID</span>
                    <span className="font-mono text-sm">
                      {payment.transaction_id || "N/A"}
                    </span>
                  </div>
                  <Separator />
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Created At</span>
                    <span>
                      {payment.created_at
                        ? formatDate(payment.created_at)
                        : "Date not available"}
                    </span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Subscription Details</CardTitle>
                  <CardDescription>
                    Information about the related subscription
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h3 className="font-medium">Member</h3>
                    <p>
                      {payment.subscriptions?.members?.first_name
                        ? `${payment.subscriptions.members.first_name} ${
                            payment.subscriptions.members.last_name || ""
                          }`
                        : "Unknown member"}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {payment.subscriptions?.members?.email ||
                        "No email available"}
                    </p>
                  </div>
                  <Separator />
                  <div>
                    <h3 className="font-medium">Plan</h3>
                    <p>
                      {payment.subscriptions?.subscription_plans?.name ||
                        "Unknown plan"}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {payment.subscriptions?.subscription_plans?.price
                        ? `${formatCurrency(
                            payment.subscriptions.subscription_plans.price
                          )} per ${
                            payment.subscriptions.subscription_plans
                              .billing_cycle || "month"
                          }`
                        : "Price information unavailable"}
                    </p>
                  </div>
                  <Separator />
                  <div>
                    <h3 className="font-medium">Subscription Status</h3>
                    <p>
                      {payment.subscriptions?.status
                        ? `${payment.subscriptions.status
                            .charAt(0)
                            .toUpperCase()}${payment.subscriptions.status.slice(
                            1
                          )}`
                        : "Unknown"}
                    </p>
                  </div>
                </CardContent>
                <CardFooter>
                  {payment.subscriptions?.id ? (
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() =>
                        router.push(
                          `/tenant/${tenant.slug}/subscriptions/${payment.subscriptions.id}`
                        )
                      }
                    >
                      View Subscription
                    </Button>
                  ) : (
                    <Button variant="outline" className="w-full" disabled>
                      Subscription Not Available
                    </Button>
                  )}
                </CardFooter>
              </Card>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
