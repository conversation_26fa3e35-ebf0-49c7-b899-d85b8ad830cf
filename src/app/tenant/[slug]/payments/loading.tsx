import { Skeleton } from "@/components/ui/skeleton";

export default function PaymentsLoading() {
  return (
    <div className="flex min-h-screen flex-col">
      {/* Header skeleton */}
      <div className="sticky top-0 z-40 w-full border-b bg-background">
        <div className="container mx-auto flex h-16 items-center justify-between px-4 py-4 md:px-6">
          <div className="flex items-center gap-4">
            <Skeleton className="h-8 w-8 rounded-full" />
            <Skeleton className="h-6 w-32" />
          </div>
          <div className="flex items-center gap-4">
            <Skeleton className="h-8 w-8 rounded-full" />
          </div>
        </div>
      </div>
      
      <div className="flex flex-1 w-full">
        {/* Sidebar skeleton */}
        <aside className="hidden h-full w-64 flex-col border-r bg-background md:flex">
          <div className="flex flex-col gap-2 p-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <Skeleton key={i} className="h-10 w-full" />
            ))}
          </div>
        </aside>
        
        <main className="flex-1 overflow-y-auto">
          <div className="container mx-auto px-4 py-6 md:px-6">
            <div className="flex items-center justify-between mb-6">
              <div>
                <Skeleton className="h-8 w-32 mb-2" />
                <Skeleton className="h-4 w-48" />
              </div>
              <Skeleton className="h-10 w-32" />
            </div>
            
            <div className="mb-6">
              <div className="flex items-center space-x-2 mb-4">
                <Skeleton className="h-9 w-64" />
              </div>
            </div>
            
            <div className="rounded-md border">
              <div className="relative w-full overflow-auto">
                <table className="w-full caption-bottom text-sm">
                  <thead className="[&_tr]:border-b">
                    <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                      <th className="h-12 px-4 text-left align-middle font-medium">
                        <Skeleton className="h-4 w-24" />
                      </th>
                      <th className="h-12 px-4 text-left align-middle font-medium">
                        <Skeleton className="h-4 w-16" />
                      </th>
                      <th className="h-12 px-4 text-left align-middle font-medium">
                        <Skeleton className="h-4 w-20" />
                      </th>
                      <th className="h-12 px-4 text-left align-middle font-medium">
                        <Skeleton className="h-4 w-16" />
                      </th>
                      <th className="h-12 px-4 text-left align-middle font-medium">
                        <Skeleton className="h-4 w-16" />
                      </th>
                    </tr>
                  </thead>
                  <tbody className="[&_tr:last-child]:border-0">
                    {[1, 2, 3, 4, 5].map((i) => (
                      <tr
                        key={i}
                        className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"
                      >
                        <td className="p-4 align-middle">
                          <div className="flex items-center gap-3">
                            <Skeleton className="h-8 w-8 rounded-full" />
                            <Skeleton className="h-4 w-24" />
                          </div>
                        </td>
                        <td className="p-4 align-middle">
                          <Skeleton className="h-4 w-32" />
                        </td>
                        <td className="p-4 align-middle">
                          <Skeleton className="h-4 w-20" />
                        </td>
                        <td className="p-4 align-middle">
                          <Skeleton className="h-4 w-16" />
                        </td>
                        <td className="p-4 align-middle">
                          <div className="flex items-center gap-2">
                            <Skeleton className="h-8 w-8" />
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
