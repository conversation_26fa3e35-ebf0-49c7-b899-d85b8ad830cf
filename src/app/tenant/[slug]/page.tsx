import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth";
import { getUserTenants } from "@/lib/auth";
import { getDashboardStats } from "@/app/actions/dashboard";
import TenantDashboardClient from "./tenant-dashboard-client";
import { getTenantBySlug } from "@/app/actions/tenants";

interface TenantDashboardPageProps {
  params: {
    slug: string;
  };
}

export default async function TenantDashboardPage({
  params,
}: TenantDashboardPageProps) {
  try {
    const { slug } = await params;
    const user = await getCurrentUser();

    if (!user) {
      redirect("/auth/login");
    }

    const tenantResult = await getTenantBySlug(slug);

    if (!tenantResult.success || !tenantResult.data) {
      redirect("/business/select");
    }

    const tenant = tenantResult.data;

    const tenants = await getUserTenants(user.id);
    const stats = await getDashboardStats(tenant.id);

    return (
      <TenantDashboardClient
        user={user}
        tenant={tenant}
        tenants={tenants}
        stats={stats}
      />
    );
  } catch (error) {
    console.error("Error in TenantDashboardPage:", error);
    redirect("/business/select");
  }
}
