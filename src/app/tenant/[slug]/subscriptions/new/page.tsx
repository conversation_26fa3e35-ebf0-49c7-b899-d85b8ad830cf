import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth";
import { getUserTenants } from "@/lib/auth";
import NewSubscriptionClient from "./new-subscription-client";
import { getTenantBySlug } from "@/app/actions/tenants";
import { getMembers } from "@/app/actions/members";
import { getPlans } from "@/app/actions/plans";

interface NewSubscriptionPageProps {
  params: {
    slug: string;
  };
  searchParams: {
    member?: string;
  };
}

export default async function NewSubscriptionPage({
  params,
  searchParams,
}: NewSubscriptionPageProps) {
  try {
    const { slug } = await params;
    const { member: memberId } = searchParams;

    const user = await getCurrentUser();

    if (!user) {
      redirect("/auth/login");
    }

    const tenantResult = await getTenantBySlug(slug);

    if (!tenantResult.success || !tenantResult.data) {
      redirect("/business/select");
    }

    const tenant = tenantResult.data;
    const tenants = await getUserTenants(user.id);
    const membersResult = await getMembers(tenant.id);
    const members = membersResult.success ? membersResult.data : [];
    const plansResult = await getPlans(tenant.id);
    const plans = plansResult.success
      ? plansResult.data.filter((plan) => plan.is_active)
      : [];

    return (
      <NewSubscriptionClient
        user={user}
        tenant={tenant}
        tenants={tenants}
        members={members}
        plans={plans}
        preselectedMemberId={memberId}
      />
    );
  } catch (error) {
    console.error("Error in NewSubscriptionPage:", error);
    redirect(`/tenant/${params.slug}/subscriptions`);
  }
}
