"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { Arrow<PERSON><PERSON><PERSON>, CalendarIcon, Trash2 } from "lucide-react";
import { format, parseISO } from "date-fns";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { DashboardHeader } from "@/components/layout/dashboard-header";
import { DashboardSidebar } from "@/components/layout/dashboard-sidebar";
import {
  updateSubscription,
  cancelSubscription,
} from "@/app/actions/subscriptions";
import { formatCurrency, formatDate } from "@/lib/utils";

// Define the schema for updating a subscription
const updateSubscriptionSchema = z.object({
  plan_id: z.string().uuid(),
  status: z.enum(["active", "canceled", "expired", "pending"]),
  auto_renew: z.boolean(),
  end_date: z.string().optional(),
});

type UpdateSubscriptionFormValues = z.infer<typeof updateSubscriptionSchema>;

interface SubscriptionDetailClientProps {
  user: any;
  tenant: any;
  tenants: any[];
  subscription: any;
  plans: any[];
}

export default function SubscriptionDetailClient({
  user,
  tenant,
  tenants,
  subscription,
  plans,
}: SubscriptionDetailClientProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Initialize form with subscription values
  const form = useForm<UpdateSubscriptionFormValues>({
    resolver: zodResolver(updateSubscriptionSchema),
    defaultValues: {
      plan_id: subscription.plan_id,
      status: subscription.status,
      auto_renew: subscription.auto_renew,
      end_date: subscription.end_date
        ? format(parseISO(subscription.end_date), "yyyy-MM-dd")
        : undefined,
    },
  });

  async function onSubmit(data: UpdateSubscriptionFormValues) {
    setIsLoading(true);

    try {
      const result = await updateSubscription(subscription.id, data);

      if (!result.success) {
        throw new Error(result.error);
      }

      toast.success("Subscription updated successfully");
      router.refresh();
    } catch (error) {
      console.error("Error updating subscription:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to update subscription"
      );
    } finally {
      setIsLoading(false);
    }
  }

  async function handleDelete() {
    setIsLoading(true);

    try {
      const result = await cancelSubscription(subscription.id);

      if (!result.success) {
        throw new Error(result.error);
      }

      toast.success("Subscription cancelled successfully");
      router.push(`/tenant/${tenant.slug}/subscriptions`);
    } catch (error) {
      console.error("Error cancelling subscription:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to cancel subscription"
      );
    } finally {
      setIsLoading(false);
      setIsDeleteDialogOpen(false);
    }
  }

  // Function to get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-500";
      case "canceled":
        return "bg-orange-500";
      case "expired":
        return "bg-red-500";
      case "pending":
        return "bg-blue-500";
      default:
        return "bg-gray-500";
    }
  };

  // Function to get billing cycle display
  const getBillingCycleDisplay = (cycle: string) => {
    switch (cycle) {
      case "monthly":
        return "Monthly";
      case "quarterly":
        return "Quarterly";
      case "biannual":
        return "Bi-annual";
      case "annual":
        return "Annual";
      default:
        return cycle;
    }
  };

  // Function to get payment status color
  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case "succeeded":
        return "bg-green-500";
      case "failed":
        return "bg-red-500";
      case "pending":
        return "bg-blue-500";
      case "refunded":
        return "bg-orange-500";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <div className="flex min-h-screen flex-col">
      <DashboardHeader user={user} tenants={tenants} currentTenant={tenant} />
      <div className="flex flex-1 w-full">
        <DashboardSidebar tenantSlug={tenant.slug} />
        <main className="flex-1 overflow-y-auto">
          <div className="container mx-auto px-4 py-6 md:px-6">
            <Button
              variant="ghost"
              onClick={() =>
                router.push(`/tenant/${tenant.slug}/subscriptions`)
              }
              className="mb-4"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Subscriptions
            </Button>

            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-3xl font-bold">Subscription Details</h1>
                <p className="text-muted-foreground">
                  Manage subscription for {subscription.members.first_name}{" "}
                  {subscription.members.last_name}
                </p>
              </div>
              <Dialog
                open={isDeleteDialogOpen}
                onOpenChange={setIsDeleteDialogOpen}
              >
                <DialogTrigger asChild>
                  <Button variant="destructive">
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete Subscription
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Are you sure?</DialogTitle>
                    <DialogDescription>
                      This action cannot be undone. This will permanently delete
                      the subscription and all associated payment records.
                    </DialogDescription>
                  </DialogHeader>
                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => setIsDeleteDialogOpen(false)}
                      disabled={isLoading}
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={handleDelete}
                      disabled={isLoading}
                    >
                      {isLoading ? "Deleting..." : "Delete"}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Subscription Information</CardTitle>
                    <CardDescription>
                      Update subscription details
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Form {...form}>
                      <form
                        onSubmit={form.handleSubmit(onSubmit)}
                        className="space-y-6"
                      >
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <h3 className="text-sm font-medium mb-2">Member</h3>
                            <p className="text-base">
                              {subscription.members.first_name}{" "}
                              {subscription.members.last_name}
                            </p>
                            <p className="text-sm text-muted-foreground mt-1">
                              {subscription.members.email}
                            </p>
                          </div>
                          <div>
                            <h3 className="text-sm font-medium mb-2">
                              Created
                            </h3>
                            <p className="text-base">
                              {formatDate(subscription.created_at)}
                            </p>
                          </div>
                        </div>

                        <FormField
                          control={form.control}
                          name="plan_id"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Subscription Plan</FormLabel>
                              <Select
                                disabled={isLoading}
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select a plan" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {plans.map((plan) => (
                                    <SelectItem key={plan.id} value={plan.id}>
                                      {plan.name} - {formatCurrency(plan.price)}
                                      /
                                      {getBillingCycleDisplay(
                                        plan.billing_cycle
                                      ).toLowerCase()}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                Change the subscription plan
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <h3 className="text-sm font-medium mb-2">
                              Start Date
                            </h3>
                            <p className="text-base">
                              {formatDate(subscription.start_date)}
                            </p>
                          </div>

                          <FormField
                            control={form.control}
                            name="end_date"
                            render={({ field }) => (
                              <FormItem className="flex flex-col">
                                <FormLabel>End Date</FormLabel>
                                <Popover>
                                  <PopoverTrigger asChild>
                                    <FormControl>
                                      <Button
                                        variant={"outline"}
                                        className={`w-full pl-3 text-left font-normal ${
                                          !field.value
                                            ? "text-muted-foreground"
                                            : ""
                                        }`}
                                      >
                                        {field.value ? (
                                          format(new Date(field.value), "PPP")
                                        ) : (
                                          <span>No end date</span>
                                        )}
                                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                      </Button>
                                    </FormControl>
                                  </PopoverTrigger>
                                  <PopoverContent
                                    className="w-auto p-0"
                                    align="start"
                                  >
                                    <Calendar
                                      mode="single"
                                      selected={
                                        field.value
                                          ? new Date(field.value)
                                          : undefined
                                      }
                                      onSelect={(date) =>
                                        field.onChange(
                                          date
                                            ? format(date, "yyyy-MM-dd")
                                            : undefined
                                        )
                                      }
                                      disabled={(date) =>
                                        date < new Date(subscription.start_date)
                                      }
                                      initialFocus
                                    />
                                  </PopoverContent>
                                </Popover>
                                <FormDescription>
                                  When the subscription ends
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <FormField
                          control={form.control}
                          name="status"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Status</FormLabel>
                              <Select
                                disabled={isLoading}
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select a status" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="active">Active</SelectItem>
                                  <SelectItem value="pending">
                                    Pending
                                  </SelectItem>
                                  <SelectItem value="canceled">
                                    Canceled
                                  </SelectItem>
                                  <SelectItem value="expired">
                                    Expired
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                Current status of the subscription
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="auto_renew"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base">
                                  Auto Renew
                                </FormLabel>
                                <FormDescription>
                                  Automatically renew this subscription when it
                                  expires
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                  disabled={isLoading}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <Button
                          type="submit"
                          className="w-full"
                          disabled={isLoading}
                        >
                          {isLoading ? "Saving..." : "Save Changes"}
                        </Button>
                      </form>
                    </Form>
                  </CardContent>
                </Card>

                {subscription.payments && subscription.payments.length > 0 && (
                  <Card className="mt-6">
                    <CardHeader>
                      <CardTitle>Payment History</CardTitle>
                      <CardDescription>
                        All payments for this subscription
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Date</TableHead>
                            <TableHead>Amount</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Method</TableHead>
                            <TableHead>Transaction ID</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {subscription.payments.map((payment: any) => (
                            <TableRow key={payment.id}>
                              <TableCell>
                                {formatDate(payment.payment_date)}
                              </TableCell>
                              <TableCell>
                                {formatCurrency(payment.amount)}
                              </TableCell>
                              <TableCell>
                                <Badge
                                  variant="outline"
                                  className={`${getPaymentStatusColor(
                                    payment.status
                                  )} text-white`}
                                >
                                  {payment.status.charAt(0).toUpperCase() +
                                    payment.status.slice(1)}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                {payment.payment_method
                                  .split("_")
                                  .map(
                                    (word: string) =>
                                      word.charAt(0).toUpperCase() +
                                      word.slice(1)
                                  )
                                  .join(" ")}
                              </TableCell>
                              <TableCell>
                                {payment.transaction_id || "N/A"}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </CardContent>
                  </Card>
                )}
              </div>

              <div>
                <Card>
                  <CardHeader>
                    <CardTitle>Plan Details</CardTitle>
                    <CardDescription>Current subscription plan</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h3 className="font-medium">Name</h3>
                      <p>{subscription.subscription_plans.name}</p>
                    </div>
                    <div>
                      <h3 className="font-medium">Price</h3>
                      <p className="text-xl font-bold">
                        {formatCurrency(subscription.subscription_plans.price)}
                        <span className="text-sm text-muted-foreground ml-1">
                          /
                          {getBillingCycleDisplay(
                            subscription.subscription_plans.billing_cycle
                          ).toLowerCase()}
                        </span>
                      </p>
                    </div>
                    <div>
                      <h3 className="font-medium">Billing Cycle</h3>
                      <p>
                        {getBillingCycleDisplay(
                          subscription.subscription_plans.billing_cycle
                        )}
                      </p>
                    </div>
                    <div>
                      <h3 className="font-medium">Status</h3>
                      <Badge
                        variant="outline"
                        className={`${getStatusColor(
                          subscription.status
                        )} text-white mt-1`}
                      >
                        {subscription.status.charAt(0).toUpperCase() +
                          subscription.status.slice(1)}
                      </Badge>
                    </div>
                    {subscription.subscription_plans.description && (
                      <div>
                        <h3 className="font-medium">Description</h3>
                        <p className="text-sm text-muted-foreground">
                          {subscription.subscription_plans.description}
                        </p>
                      </div>
                    )}
                    {subscription.subscription_plans.features &&
                      subscription.subscription_plans.features.length > 0 && (
                        <div>
                          <h3 className="font-medium">Features</h3>
                          <ul className="list-disc list-inside text-sm text-muted-foreground">
                            {subscription.subscription_plans.features.map(
                              (feature: string, index: number) => (
                                <li key={index}>{feature}</li>
                              )
                            )}
                          </ul>
                        </div>
                      )}
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button
                      variant="outline"
                      onClick={() =>
                        router.push(
                          `/tenant/${tenant.slug}/members/${subscription.members.id}`
                        )
                      }
                    >
                      View Member
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() =>
                        router.push(`/tenant/${tenant.slug}/plans`)
                      }
                    >
                      View Plans
                    </Button>
                  </CardFooter>
                </Card>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
