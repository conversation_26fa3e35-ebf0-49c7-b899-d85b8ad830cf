import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth";
import { getUserTenants } from "@/lib/auth";
import SubscriptionDetailClient from "./subscription-detail-client";
import { getTenantBySlug } from "@/app/actions/tenants";
import { getSubscriptionById } from "@/app/actions/subscriptions";
import { getPlans } from "@/app/actions/plans";

interface SubscriptionDetailPageProps {
  params: {
    slug: string;
    id: string;
  };
}

export default async function SubscriptionDetailPage({
  params,
}: SubscriptionDetailPageProps) {
  try {
    const { slug, id } = await params;
    const user = await getCurrentUser();

    if (!user) {
      redirect("/auth/login");
    }

    const tenantResult = await getTenantBySlug(slug);

    if (!tenantResult.success || !tenantResult.data) {
      redirect("/business/select");
    }

    const tenant = tenantResult.data;
    const tenants = await getUserTenants(user.id);

    // Pass the tenant ID to ensure we only get subscriptions for this tenant
    const subscriptionResult = await getSubscriptionById(id, tenant.id);

    if (!subscriptionResult.success || !subscriptionResult.data) {
      redirect(`/tenant/${slug}/subscriptions`);
    }

    const subscription = subscriptionResult.data;

    const plansResult = await getPlans(tenant.id);
    const plans = plansResult.success
      ? plansResult.data.filter((plan) => plan.is_active)
      : [];

    return (
      <SubscriptionDetailClient
        user={user}
        tenant={tenant}
        tenants={tenants}
        subscription={subscription}
        plans={plans}
      />
    );
  } catch (error) {
    const { slug } = params;
    console.error("Error in SubscriptionDetailPage:", error);
    redirect(`/tenant/${slug}/subscriptions`);
  }
}
