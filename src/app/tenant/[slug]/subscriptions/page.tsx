import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth";
import { getUserTenants } from "@/lib/auth";
import SubscriptionsClient from "./subscriptions-client";
import { getTenantBySlug } from "@/app/actions/tenants";
import { getSubscriptions } from "@/app/actions/subscriptions";

interface SubscriptionsPageProps {
  params: {
    slug: string;
  };
}

export default async function SubscriptionsPage({
  params,
}: SubscriptionsPageProps) {
  try {
    const { slug } = await params;
    const user = await getCurrentUser();

    if (!user) {
      redirect("/auth/login");
    }

    const tenantResult = await getTenantBySlug(slug);

    if (!tenantResult.success || !tenantResult.data) {
      redirect("/business/select");
    }

    const tenant = tenantResult.data;
    const tenants = await getUserTenants(user.id);
    const subscriptionsResult = await getSubscriptions(tenant.id);
    const subscriptions = subscriptionsResult.success
      ? subscriptionsResult.data
      : [];

    return (
      <SubscriptionsClient
        user={user}
        tenant={tenant}
        tenants={tenants}
        subscriptions={subscriptions}
      />
    );
  } catch (error) {
    console.error("Error in SubscriptionsPage:", error);
    redirect("/business/select");
  }
}
