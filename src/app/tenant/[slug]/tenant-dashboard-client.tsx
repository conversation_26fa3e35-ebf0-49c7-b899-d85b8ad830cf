"use client";

import Link from "next/link";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { formatCurrency, formatDate } from "@/lib/utils";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { DashboardSidebar } from "@/components/layout/dashboard-sidebar";
import { DashboardHeader } from "@/components/layout/dashboard-header";

interface TenantDashboardClientProps {
  user: any;
  tenant: any;
  tenants: any[];
  stats: any;
}

export default function TenantDashboardClient({
  user,
  tenant,
  tenants,
  stats,
}: TenantDashboardClientProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if we have a valid user
    if (!user) {
      router.push("/auth/login");
      return;
    }

    setIsLoading(false);
  }, [user, router]);

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        Loading...
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col">
      <DashboardHeader user={user} tenants={tenants} currentTenant={tenant} />
      <div className="flex flex-1 w-full">
        <DashboardSidebar tenantSlug={tenant.slug} />
        <main className="flex-1 overflow-y-auto">
          <div className="container mx-auto px-4 py-6 md:px-6">
            <div className="space-y-6">
              <div>
                <h1 className="text-3xl font-bold">{tenant.name} Dashboard</h1>
                <p className="text-muted-foreground">
                  Overview of your business
                </p>
              </div>

              <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">
                      Total Members
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {stats.totalMembers}
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">
                      Active Members
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {stats.activeMembers}
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">
                      Active Subscriptions
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {stats.activeSubscriptions}
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">
                      Revenue (Last 30 Days)
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatCurrency(stats.revenueStats.last30Days)}
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="grid gap-6 md:grid-cols-2">
                <Card className="col-span-1">
                  <CardHeader>
                    <CardTitle>Upcoming Renewals</CardTitle>
                    <CardDescription>
                      Subscriptions renewing in the next 7 days
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {stats.upcomingRenewals.length > 0 ? (
                      <div className="space-y-4">
                        {stats.upcomingRenewals.map((renewal: any) => (
                          <div
                            key={renewal.id}
                            className="flex items-center justify-between"
                          >
                            <div>
                              <p className="font-medium">
                                {renewal.members.first_name}{" "}
                                {renewal.members.last_name}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                {renewal.subscription_plans.name} -{" "}
                                {formatCurrency(
                                  renewal.subscription_plans.price
                                )}
                              </p>
                            </div>
                            <div className="text-sm">
                              {formatDate(renewal.end_date)}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">
                        No upcoming renewals in the next 7 days
                      </p>
                    )}
                  </CardContent>
                </Card>

                <Card className="col-span-1">
                  <CardHeader>
                    <CardTitle>Recent Payments</CardTitle>
                    <CardDescription>
                      Latest payment transactions
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {stats.recentPayments.length > 0 ? (
                      <div className="space-y-4">
                        {stats.recentPayments.map((payment: any) => (
                          <div
                            key={payment.id}
                            className="flex items-center justify-between"
                          >
                            <div>
                              <p className="font-medium">
                                {payment.subscriptions.members.first_name}{" "}
                                {payment.subscriptions.members.last_name}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                {payment.payment_method} - {payment.status}
                              </p>
                            </div>
                            <div className="text-right">
                              <p className="font-medium">
                                {formatCurrency(payment.amount)}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                {formatDate(payment.payment_date)}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">
                        No recent payments
                      </p>
                    )}
                  </CardContent>
                </Card>
              </div>

              <div className="flex justify-end space-x-4">
                <Button asChild>
                  <Link href={`/tenant/${tenant.slug}/members/new`}>
                    Add Member
                  </Link>
                </Button>
                <Button asChild>
                  <Link href={`/tenant/${tenant.slug}/plans/new`}>
                    Create Plan
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
