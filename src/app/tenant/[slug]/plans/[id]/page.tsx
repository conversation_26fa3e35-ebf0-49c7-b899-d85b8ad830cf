import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth";
import { getUserTenants } from "@/lib/auth";
import PlanDetailClient from "./plan-detail-client";
import { getTenantBySlug } from "@/app/actions/tenants";
import { getPlanById } from "@/app/actions/plans";

interface PlanDetailPageProps {
  params: {
    slug: string;
    id: string;
  };
}

export default async function PlanDetailPage({ params }: PlanDetailPageProps) {
  try {
    const { slug, id } = await params;
    const user = await getCurrentUser();

    if (!user) {
      redirect("/auth/login");
    }

    const tenantResult = await getTenantBySlug(slug);

    if (!tenantResult.success || !tenantResult.data) {
      redirect("/business/select");
    }

    const tenant = tenantResult.data;
    const tenants = await getUserTenants(user.id);
    const planResult = await getPlanById(id, tenant.id);

    if (!planResult.success || !planResult.data) {
      redirect(`/tenant/${slug}/plans`);
    }

    const plan = planResult.data;

    return (
      <PlanDetailClient
        user={user}
        tenant={tenant}
        tenants={tenants}
        plan={plan}
      />
    );
  } catch (error) {
    console.error("Error in PlanDetailPage:", error);
    redirect(`/tenant/${params.slug}/plans`);
  }
}
