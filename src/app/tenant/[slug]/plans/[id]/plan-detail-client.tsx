"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { ArrowLeft, Plus, Trash2, X } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogD<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Dialog<PERSON>rigger,
} from "@/components/ui/dialog";
import { DashboardHeader } from "@/components/layout/dashboard-header";
import { DashboardSidebar } from "@/components/layout/dashboard-sidebar";
import { subscriptionPlanSchema } from "@/lib/validations";
// import { updateSubscriptionPlan, deleteSubscriptionPlan } from "@/lib/plans";
import { formatCurrency } from "@/lib/utils";

// Define the schema for updating a plan
const updatePlanSchema = subscriptionPlanSchema.partial().required({
  name: true,
  price: true,
  billing_cycle: true,
  is_active: true,
});

type UpdatePlanFormValues = z.infer<typeof updatePlanSchema>;

interface PlanDetailClientProps {
  user: any;
  tenant: any;
  tenants: any[];
  plan: any;
}

export default function PlanDetailClient({
  user,
  tenant,
  tenants,
  plan,
}: PlanDetailClientProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [features, setFeatures] = useState<string[]>(plan.features || []);
  const [featureInput, setFeatureInput] = useState("");

  // Initialize form with plan values
  const form = useForm<UpdatePlanFormValues>({
    resolver: zodResolver(updatePlanSchema),
    defaultValues: {
      name: plan.name,
      description: plan.description || "",
      price: plan.price,
      billing_cycle: plan.billing_cycle,
      features: plan.features || [],
      is_active: plan.is_active,
    },
  });

  // Add a feature to the list
  const addFeature = () => {
    if (featureInput.trim() === "") return;

    const newFeatures = [...features, featureInput.trim()];
    setFeatures(newFeatures);
    form.setValue("features", newFeatures);
    setFeatureInput("");
  };

  // Remove a feature from the list
  const removeFeature = (index: number) => {
    const newFeatures = features.filter((_, i) => i !== index);
    setFeatures(newFeatures);
    form.setValue("features", newFeatures);
  };

  // Handle form submission
  async function onSubmit(data: UpdatePlanFormValues) {
    setIsLoading(true);

    try {
      // await updateSubscriptionPlan(plan.id, data);

      toast.success("Subscription plan updated successfully");
      router.refresh();
    } catch (error) {
      console.error("Error updating subscription plan:", error);
      toast.error("Failed to update subscription plan");
    } finally {
      setIsLoading(false);
    }
  }

  // Handle plan deletion
  async function handleDelete() {
    setIsLoading(true);

    try {
      // await deleteSubscriptionPlan(plan.id);

      toast.success("Subscription plan deleted successfully");
      router.push(`/tenant/${tenant.slug}/plans`);
    } catch (error) {
      console.error("Error deleting subscription plan:", error);
      toast.error("Failed to delete subscription plan");
    } finally {
      setIsLoading(false);
      setIsDeleteDialogOpen(false);
    }
  }

  // Function to get billing cycle display
  const getBillingCycleDisplay = (cycle: string) => {
    switch (cycle) {
      case "monthly":
        return "Monthly";
      case "quarterly":
        return "Quarterly";
      case "biannual":
        return "Bi-annual";
      case "annual":
        return "Annual";
      default:
        return cycle;
    }
  };

  return (
    <div className="flex min-h-screen flex-col">
      <DashboardHeader user={user} tenants={tenants} currentTenant={tenant} />
      <div className="flex flex-1 w-full">
        <DashboardSidebar tenantSlug={tenant.slug} />
        <main className="flex-1 overflow-y-auto">
          <div className="container mx-auto px-4 py-6 md:px-6">
            <Button
              variant="ghost"
              onClick={() => router.push(`/tenant/${tenant.slug}/plans`)}
              className="mb-4"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Plans
            </Button>

            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-3xl font-bold">Edit Plan</h1>
                <p className="text-muted-foreground">
                  Update subscription plan details
                </p>
              </div>
              <Dialog
                open={isDeleteDialogOpen}
                onOpenChange={setIsDeleteDialogOpen}
              >
                <DialogTrigger asChild>
                  <Button variant="destructive">
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete Plan
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Are you sure?</DialogTitle>
                    <DialogDescription>
                      This action cannot be undone. This will permanently delete
                      the subscription plan. Active subscriptions using this
                      plan will not be affected.
                    </DialogDescription>
                  </DialogHeader>
                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => setIsDeleteDialogOpen(false)}
                      disabled={isLoading}
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={handleDelete}
                      disabled={isLoading}
                    >
                      {isLoading ? "Deleting..." : "Delete"}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Plan Details</CardTitle>
                    <CardDescription>
                      Update the details for this subscription plan
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Form {...form}>
                      <form
                        onSubmit={form.handleSubmit(onSubmit)}
                        className="space-y-6"
                      >
                        <FormField
                          control={form.control}
                          name="name"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Plan Name</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="Premium Membership"
                                  disabled={isLoading}
                                  {...field}
                                />
                              </FormControl>
                              <FormDescription>
                                The name of your subscription plan
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="description"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Description</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Full access to all gym facilities and classes"
                                  disabled={isLoading}
                                  {...field}
                                  value={field.value || ""}
                                />
                              </FormControl>
                              <FormDescription>
                                A brief description of what this plan offers
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <FormField
                            control={form.control}
                            name="price"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Price</FormLabel>
                                <FormControl>
                                  <div className="relative">
                                    <span className="absolute left-3 top-2.5">
                                      $
                                    </span>
                                    <Input
                                      type="number"
                                      min={0}
                                      step={0.01}
                                      placeholder="99.99"
                                      disabled={isLoading}
                                      className="pl-7"
                                      {...field}
                                      onChange={(e) =>
                                        field.onChange(
                                          parseFloat(e.target.value)
                                        )
                                      }
                                    />
                                  </div>
                                </FormControl>
                                <FormDescription>
                                  The price of the subscription plan
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="billing_cycle"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Billing Cycle</FormLabel>
                                <Select
                                  disabled={isLoading}
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select a billing cycle" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="monthly">
                                      Monthly
                                    </SelectItem>
                                    <SelectItem value="quarterly">
                                      Quarterly
                                    </SelectItem>
                                    <SelectItem value="biannual">
                                      Bi-annual
                                    </SelectItem>
                                    <SelectItem value="annual">
                                      Annual
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormDescription>
                                  How often members will be billed
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <div>
                          <FormLabel>Features</FormLabel>
                          <div className="flex items-center mt-2 mb-2">
                            <Input
                              placeholder="Add a feature"
                              value={featureInput}
                              onChange={(e) => setFeatureInput(e.target.value)}
                              onKeyDown={(e) => {
                                if (e.key === "Enter") {
                                  e.preventDefault();
                                  addFeature();
                                }
                              }}
                              disabled={isLoading}
                              className="mr-2"
                            />
                            <Button
                              type="button"
                              onClick={addFeature}
                              disabled={isLoading || featureInput.trim() === ""}
                            >
                              <Plus className="h-4 w-4" />
                            </Button>
                          </div>
                          <FormDescription>
                            Add features included in this plan (e.g., "Unlimited
                            gym access", "Personal trainer")
                          </FormDescription>

                          <div className="flex flex-wrap gap-2 mt-3">
                            {features.map((feature, index) => (
                              <Badge
                                key={index}
                                variant="secondary"
                                className="flex items-center gap-1"
                              >
                                {feature}
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  className="h-4 w-4 p-0 ml-1"
                                  onClick={() => removeFeature(index)}
                                  disabled={isLoading}
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              </Badge>
                            ))}
                          </div>
                        </div>

                        <FormField
                          control={form.control}
                          name="is_active"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base">
                                  Active
                                </FormLabel>
                                <FormDescription>
                                  Make this plan available for new subscriptions
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                  disabled={isLoading}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <Button
                          type="submit"
                          className="w-full"
                          disabled={isLoading}
                        >
                          {isLoading ? "Saving..." : "Save Changes"}
                        </Button>
                      </form>
                    </Form>
                  </CardContent>
                </Card>
              </div>

              <div>
                <Card>
                  <CardHeader>
                    <CardTitle>Plan Preview</CardTitle>
                    <CardDescription>
                      How your plan will appear to members
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h3 className="font-medium">Name</h3>
                      <p className="text-xl font-bold">{form.watch("name")}</p>
                    </div>
                    {form.watch("description") && (
                      <div>
                        <h3 className="font-medium">Description</h3>
                        <p className="text-sm text-muted-foreground">
                          {form.watch("description")}
                        </p>
                      </div>
                    )}
                    <div>
                      <h3 className="font-medium">Price</h3>
                      <p className="text-xl font-bold">
                        {formatCurrency(form.watch("price"))}
                        <span className="text-sm text-muted-foreground ml-1">
                          /
                          {getBillingCycleDisplay(
                            form.watch("billing_cycle")
                          ).toLowerCase()}
                        </span>
                      </p>
                    </div>
                    <div>
                      <h3 className="font-medium">Billing Cycle</h3>
                      <p>
                        {getBillingCycleDisplay(form.watch("billing_cycle"))}
                      </p>
                    </div>
                    <div>
                      <h3 className="font-medium">Status</h3>
                      <Badge
                        variant="outline"
                        className={`${
                          form.watch("is_active")
                            ? "bg-green-500"
                            : "bg-red-500"
                        } text-white mt-1`}
                      >
                        {form.watch("is_active") ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                    {features.length > 0 && (
                      <div>
                        <h3 className="font-medium">Features</h3>
                        <ul className="list-disc list-inside text-sm text-muted-foreground mt-2">
                          {features.map((feature, index) => (
                            <li key={index}>{feature}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
