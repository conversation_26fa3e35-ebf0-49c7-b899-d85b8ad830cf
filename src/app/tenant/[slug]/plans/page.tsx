import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth";
import { getUserTenants } from "@/lib/auth";
import PlansClient from "./plans-client";
import { getTenantBySlug } from "@/app/actions/tenants";
import { getPlans } from "@/app/actions/plans";

interface PlansPageProps {
  params: {
    slug: string;
  };
}

export default async function PlansPage({ params }: PlansPageProps) {
  try {
    const { slug } = await params;
    const user = await getCurrentUser();

    if (!user) {
      redirect("/auth/login");
    }

    const tenantResult = await getTenantBySlug(slug);

    if (!tenantResult.success || !tenantResult.data) {
      redirect("/business/select");
    }

    const tenant = tenantResult.data;
    const tenants = await getUserTenants(user.id);
    const plansResult = await getPlans(tenant.id);
    const plans = plansResult.success ? plansResult.data : [];

    return (
      <PlansClient
        user={user}
        tenant={tenant}
        tenants={tenants}
        plans={plans}
      />
    );
  } catch (error) {
    console.error("Error in PlansPage:", error);
    redirect("/business/select");
  }
}
