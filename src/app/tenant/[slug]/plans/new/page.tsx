import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth";
import { getTenantBySlug } from "@/app/actions/tenants";
import { getUserTenants } from "@/lib/auth";
import NewPlanClient from "./new-plan-client";

interface NewPlanPageProps {
  params: {
    slug: string;
  };
}

export default async function NewPlanPage({ params }: NewPlanPageProps) {
  try {
    const { slug } = params;
    const user = await getCurrentUser();

    if (!user) {
      redirect("/auth/login");
    }

    const tenantResult = await getTenantBySlug(slug);

    if (!tenantResult.success || !tenantResult.data) {
      console.error("Failed to get tenant:", tenantResult.error);
      redirect("/business/select");
    }

    const tenant = tenantResult.data;
    const tenants = await getUserTenants(user.id);

    return <NewPlanClient user={user} tenant={tenant} tenants={tenants} />;
  } catch (error) {
    console.error("Error in NewPlanPage:", error);
    redirect(`/tenant/${params.slug}/plans`);
  }
}
