import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth";
import { getUserTenants } from "@/app/actions/tenants";
import { BusinessSelectionClient } from "./client";

export const dynamic = "force-dynamic";

export default async function BusinessSelectionPage() {
  const user = await getCurrentUser();

  if (!user) {
    console.log("No authenticated user found, redirecting to login");
    redirect("/auth/login");
  }

  console.log("Server: Authenticated user:", user.id, user.email);

  // Pre-fetch tenants on the server
  const tenantsResult = await getUserTenants();
  const tenants = tenantsResult.success ? tenantsResult.data : [];
  console.log("Server: User tenants count:", tenants.length);

  // If there's only one tenant, redirect to it automatically
  if (tenants.length === 1) {
    redirect(`/tenant/${tenants[0].tenants.slug}`);
  }

  return <BusinessSelectionClient userId={user.id} />;
}
