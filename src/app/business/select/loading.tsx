import { Skeleton } from "@/components/ui/skeleton";

export default function BusinessSelectionLoading() {
  return (
    <div className="container mx-auto max-w-6xl py-10">
      <div className="mb-8 text-center">
        <Skeleton className="h-8 w-64 mx-auto mb-2" />
        <Skeleton className="h-4 w-80 mx-auto" />
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {[1, 2, 3].map((i) => (
          <div key={i} className="rounded-lg border p-6">
            <div className="flex items-center justify-center mb-4">
              <Skeleton className="h-16 w-16 rounded-full" />
            </div>
            <div className="text-center mb-4">
              <Skeleton className="h-6 w-32 mx-auto mb-2" />
              <Skeleton className="h-4 w-24 mx-auto" />
            </div>
            <Skeleton className="h-10 w-full" />
          </div>
        ))}
        
        <div className="rounded-lg border border-dashed p-6 flex flex-col items-center justify-center">
          <Skeleton className="h-16 w-16 rounded-full mb-4" />
          <Skeleton className="h-6 w-32 mb-2" />
          <Skeleton className="h-4 w-48 mb-4" />
          <Skeleton className="h-10 w-full" />
        </div>
      </div>
    </div>
  );
}
