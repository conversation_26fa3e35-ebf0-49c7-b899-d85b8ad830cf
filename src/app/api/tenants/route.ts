import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { createTenantSchema } from "@/lib/validations";
import { createTenant } from "@/app/actions/tenants";

export async function GET(req: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { message: "Missing or invalid authorization header" },
        { status: 401 }
      );
    }

    // We don't need the token anymore as we're using the server client
    // which gets the session from cookies

    // Use the server supabase client
    const supabase = await createClient();

    // Get the user
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError || !userData.user) {
      console.error("Error getting user:", userError);
      return NextResponse.json(
        { message: "Invalid authentication token" },
        { status: 401 }
      );
    }

    // User ID is available in userData.user.id

    // Get the user's tenants
    const { data: tenants, error: tenantsError } = await supabase
      .from("tenant_users")
      .select(
        `
        tenant_id,
        role,
        tenants:tenant_id(
          id,
          name,
          slug,
          logo_url
        )
      `
      )
      .eq("user_id", userData.user.id);

    if (tenantsError) {
      console.error("Error fetching tenants:", tenantsError);
      return NextResponse.json(
        { message: "Error fetching tenants" },
        { status: 500 }
      );
    }

    return NextResponse.json({ tenants: tenants || [] });
  } catch (error) {
    console.error("Unexpected error:", error);
    return NextResponse.json(
      { message: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = req.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { message: "Missing or invalid authorization header" },
        { status: 401 }
      );
    }

    // We don't need the token anymore as we're using the server client
    // which gets the session from cookies

    const body = await req.json();

    // Validate request body
    const validatedData = createTenantSchema.parse(body);

    // Use the server supabase client
    const supabase = await createClient();

    // Get the user
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError || !userData.user) {
      console.error("Error getting user:", userError);
      return NextResponse.json(
        { message: "Invalid authentication token" },
        { status: 401 }
      );
    }

    // User ID is available in userData.user.id

    // Check if tenant with this slug already exists
    const { data: existingTenant } = await supabase
      .from("tenants")
      .select("id")
      .eq("slug", validatedData.slug)
      .maybeSingle();

    if (existingTenant) {
      return NextResponse.json(
        { message: "A business with this URL already exists" },
        { status: 400 }
      );
    }

    console.log("Creating tenant with data:", {
      name: validatedData.name,
      slug: validatedData.slug,
      userId: userData.user.id,
    });

    // Create tenant
    try {
      const result = await createTenant({
        name: validatedData.name,
        slug: validatedData.slug,
      });

      if (!result.success) {
        throw new Error(result.error);
      }

      console.log("Tenant created successfully:", result.data);

      return NextResponse.json(result.data, { status: 201 });
    } catch (error) {
      console.error("Error in createTenant function:", error);
      return NextResponse.json(
        {
          message:
            error instanceof Error ? error.message : "Failed to create tenant",
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Create tenant error:", error);

    if (error instanceof Error) {
      return NextResponse.json({ message: error.message }, { status: 400 });
    }

    return NextResponse.json(
      { message: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
