import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { getUserTenants } from "@/lib/supabase/tenants";

export async function GET(req: NextRequest) {
  try {
    // Get the user from the session
    const supabase = await createClient();
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError || !userData.user) {
      console.error("Error getting user:", userError);
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 }
      );
    }

    console.log(
      "API: Authenticated user:",
      userData.user.id,
      userData.user.email
    );

    // Use the helper function to get all tenants for the user
    const tenants = await getUserTenants(userData.user.id);

    console.log("API: Returning all tenants:", tenants);
    return NextResponse.json({ tenants });
  } catch (error) {
    console.error("Unexpected error in tenants API:", error);
    return NextResponse.json(
      { message: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
