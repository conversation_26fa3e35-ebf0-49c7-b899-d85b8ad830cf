"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useMemberAuth } from "@/components/providers/member-auth-provider";
import MemberProfileClient from "./member-profile-client";

export default function MemberProfilePage() {
  const { user, member, isLoading } = useMemberAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !user) {
      router.push("/member/login");
    }
  }, [isLoading, user, router]);

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user || !member) {
    return null; // Will redirect in useEffect
  }

  return <MemberProfileClient member={member} />;
}
