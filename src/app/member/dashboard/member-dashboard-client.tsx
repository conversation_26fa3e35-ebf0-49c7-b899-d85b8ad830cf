"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useMemberAuth } from "@/components/providers/member-auth-provider";
import { createClient } from "@/utils/supabase/client";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { formatCurrency, formatDate } from "@/lib/utils";
import {
  User,
  LogOut,
  CreditCard,
  Calendar,
  Clock,
  ChevronDown,
} from "lucide-react";

interface MemberDashboardClientProps {
  member: any;
}

export default function MemberDashboardClient({
  member,
}: MemberDashboardClientProps) {
  const { logout } = useMemberAuth();
  const router = useRouter();
  const [subscriptions, setSubscriptions] = useState<any[]>([]);
  const [payments, setPayments] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      console.log("Current member data:", member);
      console.log(
        "Fetching subscriptions for member ID:",
        member.id,
        "and tenant ID:",
        member.tenant_id
      );
      try {
        const supabase = createClient();

        console.log("Attempting to fetch subscriptions with params:", {
          member_id: member.id,
          tenant_id: member.tenant_id,
        });

        // Let's try a direct query first to see if it works
        const { data: directSubscriptions, error: directError } = await supabase
          .from("subscriptions")
          .select("*")
          .eq("member_id", member.id);

        console.log(
          "Direct subscriptions query result:",
          directSubscriptions,
          directError
        );

        // Now try to get subscription plans for this tenant
        const { data: plans, error: plansError } = await supabase
          .from("subscription_plans")
          .select("*")
          .eq("tenant_id", member.tenant_id);

        console.log("Plans for tenant:", plans, plansError);

        // Get subscriptions with plans using a direct query with proper filtering
        const {
          data: subscriptionsWithPlansData,
          error: subscriptionsWithPlansError,
        } = await supabase
          .from("subscriptions")
          .select(
            `
              *,
              subscription_plans!inner(*)
            `
          )
          .eq("member_id", member.id)
          .eq("subscription_plans.tenant_id", member.tenant_id);

        console.log(
          "Direct subscriptions with plans query result:",
          subscriptionsWithPlansData,
          subscriptionsWithPlansError
        );

        if (subscriptionsError) {
          console.error("RPC function error:", subscriptionsError);
          // Don't throw, let's try a fallback approach
        }

        // Use the direct query results
        let subscriptionsWithPlans = [];

        // If we got subscriptions with plans directly, use them
        if (
          subscriptionsWithPlansData &&
          subscriptionsWithPlansData.length > 0
        ) {
          console.log("Using direct subscriptions with plans query results");
          subscriptionsWithPlans = subscriptionsWithPlansData;
        }
        // Fallback: If we have direct subscriptions and plans, try to match them
        else if (
          directSubscriptions &&
          directSubscriptions.length > 0 &&
          plans &&
          plans.length > 0
        ) {
          console.log("Using fallback approach with direct queries");

          // Find subscriptions that match plans for this tenant
          const matchingSubscriptions = directSubscriptions.filter((sub) => {
            return plans.some((plan) => plan.id === sub.plan_id);
          });

          console.log("Matching subscriptions:", matchingSubscriptions);

          // Combine subscriptions with their plans
          subscriptionsWithPlans = matchingSubscriptions.map((sub) => {
            const plan = plans.find((p) => p.id === sub.plan_id);
            return {
              ...sub,
              subscription_plans: plan,
            };
          });
        }

        console.log("Subscriptions with plans:", subscriptionsWithPlans);
        setSubscriptions(subscriptionsWithPlans || []);

        // Check if we have any subscriptions to fetch payments for
        if (subscriptionsWithPlans && subscriptionsWithPlans.length > 0) {
          const subscriptionIds = subscriptionsWithPlans.map((sub) => sub.id);
          console.log(
            "Fetching payments for subscription IDs:",
            subscriptionIds
          );

          try {
            // First try the standard query
            const { data: paymentsData, error: paymentsError } = await supabase
              .from("payments")
              .select(
                `
                *,
                subscriptions(
                  *,
                  subscription_plans(*)
                )
              `
              )
              .in("subscription_id", subscriptionIds)
              .order("payment_date", { ascending: false });

            console.log(
              "Standard payments query result:",
              paymentsData,
              paymentsError
            );

            // If the standard query fails, try a more direct approach
            if (paymentsError) {
              console.log(
                "Standard payments query failed, trying direct approach"
              );

              // Try a more direct query with proper filtering
              const { data: directPaymentsData, error: directPaymentsError } =
                await supabase
                  .from("payments")
                  .select(
                    `
                    *,
                    subscriptions!inner(
                      *,
                      subscription_plans!inner(*)
                    )
                  `
                  )
                  .eq("subscriptions.member_id", member.id)
                  .eq(
                    "subscriptions.subscription_plans.tenant_id",
                    member.tenant_id
                  )
                  .order("payment_date", { ascending: false });

              console.log(
                "Direct payments query result:",
                directPaymentsData,
                directPaymentsError
              );

              if (directPaymentsError) {
                console.error(
                  "Both payment queries failed:",
                  directPaymentsError
                );
                setPayments([]);
              } else {
                setPayments(directPaymentsData || []);
              }
            } else {
              setPayments(paymentsData || []);
            }
          } catch (error) {
            console.error("Exception fetching payments:", error);
            setPayments([]);
          }
        } else {
          console.log("No subscriptions to fetch payments for");
          setPayments([]);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [member.id, member.tenant_id]);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-500";
      case "pending":
        return "bg-yellow-500";
      case "canceled":
      case "cancelled":
        return "bg-red-500";
      case "expired":
        return "bg-gray-500";
      default:
        return "bg-gray-500";
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case "succeeded":
        return "bg-green-500";
      case "pending":
        return "bg-yellow-500";
      case "failed":
        return "bg-red-500";
      case "refunded":
        return "bg-blue-500";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="sticky top-0 z-40 w-full border-b bg-background">
        <div className="container mx-auto flex h-16 items-center justify-between px-4 py-4 md:px-6">
          <div className="flex items-center gap-2">
            {member.tenants?.logo_url ? (
              <Image
                src={member.tenants.logo_url}
                alt={member.tenants.name}
                width={32}
                height={32}
                className="rounded-full"
              />
            ) : (
              <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-bold">
                {member.tenants?.name?.charAt(0) || "G"}
              </div>
            )}
            <span className="text-xl font-bold">
              {member.tenants?.name || "Gym"}
            </span>
          </div>
          <div className="flex items-center gap-4">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="flex items-center gap-2">
                  <span className="hidden md:inline-block">
                    {member.first_name} {member.last_name}
                  </span>
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/member/profile">
                    <User className="mr-2 h-4 w-4" />
                    Profile
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => logout()}>
                  <LogOut className="mr-2 h-4 w-4" />
                  Logout
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8 md:px-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Welcome, {member.first_name}!</h1>
          <p className="text-muted-foreground">
            Here's an overview of your membership information
          </p>
        </div>

        {member.all_tenants && member.all_tenants.length > 1 && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Your Memberships</CardTitle>
              <CardDescription>
                You have memberships at multiple gyms
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {member.all_tenants.map((tenant: any, index: number) => (
                  <div
                    key={tenant.id}
                    className={`flex items-center p-4 rounded-lg border ${
                      tenant.id === member.tenant_id
                        ? "bg-primary/10 border-primary/30"
                        : "bg-card hover:bg-accent/50 cursor-pointer"
                    }`}
                    onClick={() => {
                      if (tenant.id !== member.tenant_id) {
                        // Find the member record for this tenant
                        const membershipForTenant = member.all_memberships.find(
                          (m: any) => m.tenant_id === tenant.id
                        );
                        if (membershipForTenant) {
                          // Switch to this tenant's view
                          window.location.href = `/member/dashboard?tenant=${tenant.id}`;
                        }
                      }
                    }}
                  >
                    {tenant.logo_url ? (
                      <Image
                        src={tenant.logo_url}
                        alt={tenant.name}
                        width={40}
                        height={40}
                        className="rounded-full mr-4"
                      />
                    ) : (
                      <div className="h-10 w-10 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-bold mr-4">
                        {tenant.name?.charAt(0) || "G"}
                      </div>
                    )}
                    <div>
                      <p className="font-medium">{tenant.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {tenant.id === member.tenant_id
                          ? "Current view"
                          : "Click to switch"}
                      </p>
                    </div>
                    {tenant.id === member.tenant_id && (
                      <Badge className="ml-auto" variant="outline">
                        Active
                      </Badge>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        <Tabs defaultValue="subscriptions" className="space-y-6">
          <TabsList>
            <TabsTrigger value="subscriptions">Subscriptions</TabsTrigger>
            <TabsTrigger value="payments">Payment History</TabsTrigger>
          </TabsList>

          <TabsContent value="subscriptions" className="space-y-6">
            {isLoading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
                <p className="mt-4 text-lg">Loading subscriptions...</p>
              </div>
            ) : subscriptions.length > 0 ? (
              <div className="grid gap-6 md:grid-cols-2">
                {subscriptions.map((subscription) => (
                  <Card key={subscription.id}>
                    <CardHeader>
                      <div className="flex justify-between items-center">
                        <CardTitle>
                          {subscription.subscription_plans?.name}
                        </CardTitle>
                        <Badge
                          variant="outline"
                          className={`${getStatusColor(
                            subscription.status
                          )} text-white`}
                        >
                          {subscription.status.charAt(0).toUpperCase() +
                            subscription.status.slice(1)}
                        </Badge>
                      </div>
                      <CardDescription>
                        {formatCurrency(subscription.subscription_plans?.price)}{" "}
                        per {subscription.subscription_plans?.billing_cycle}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="flex items-center">
                        <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                        <span>
                          Start Date: {formatDate(subscription.start_date)}
                        </span>
                      </div>
                      {subscription.end_date && (
                        <div className="flex items-center">
                          <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                          <span>
                            End Date: {formatDate(subscription.end_date)}
                          </span>
                        </div>
                      )}
                      <div className="flex items-center">
                        <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                        <span>
                          Auto Renew:{" "}
                          {subscription.auto_renew ? "Enabled" : "Disabled"}
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <h2 className="text-xl font-semibold mb-2">
                  No Subscriptions Found
                </h2>
                <p className="text-muted-foreground">
                  You don't have any active subscriptions at the moment.
                </p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="payments" className="space-y-6">
            {isLoading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
                <p className="mt-4 text-lg">Loading payment history...</p>
              </div>
            ) : payments.length > 0 ? (
              <Card>
                <CardHeader>
                  <CardTitle>Payment History</CardTitle>
                  <CardDescription>
                    Your recent payment transactions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-3 px-2">Date</th>
                          <th className="text-left py-3 px-2">Plan</th>
                          <th className="text-left py-3 px-2">Amount</th>
                          <th className="text-left py-3 px-2">Status</th>
                          <th className="text-left py-3 px-2">Method</th>
                        </tr>
                      </thead>
                      <tbody>
                        {payments.map((payment) => (
                          <tr key={payment.id} className="border-b">
                            <td className="py-3 px-2">
                              {formatDate(payment.payment_date)}
                            </td>
                            <td className="py-3 px-2">
                              {payment.subscriptions?.subscription_plans
                                ?.name || "N/A"}
                            </td>
                            <td className="py-3 px-2">
                              {formatCurrency(payment.amount)}
                            </td>
                            <td className="py-3 px-2">
                              <Badge
                                variant="outline"
                                className={`${getPaymentStatusColor(
                                  payment.status
                                )} text-white`}
                              >
                                {payment.status.charAt(0).toUpperCase() +
                                  payment.status.slice(1)}
                              </Badge>
                            </td>
                            <td className="py-3 px-2">
                              {payment.payment_method
                                .split("_")
                                .map(
                                  (word: string) =>
                                    word.charAt(0).toUpperCase() + word.slice(1)
                                )
                                .join(" ")}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <div className="text-center py-12">
                <h2 className="text-xl font-semibold mb-2">
                  No Payments Found
                </h2>
                <p className="text-muted-foreground">
                  You don't have any payment records at the moment.
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
}
