"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useMemberAuth } from "@/components/providers/member-auth-provider";
import MemberDashboardClient from "./member-dashboard-client";

export default function MemberDashboardPage() {
  const { user, member, isLoading } = useMemberAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeMember, setActiveMember] = useState<any>(null);

  useEffect(() => {
    if (!isLoading && !user) {
      router.push("/member/login");
    }
  }, [isLoading, user, router]);

  console.log(member);

  useEffect(() => {
    if (member && member.all_memberships) {
      // Check if there's a tenant parameter in the URL
      const tenantId = searchParams.get("tenant");

      if (tenantId) {
        // Find the membership for this tenant
        const membershipForTenant = member.all_memberships.find(
          (m: any) => m.tenant_id === tenantId
        );

        if (membershipForTenant) {
          // Create a modified member object with this tenant as the primary one
          const modifiedMember = {
            ...member, // Keep all original member properties
            tenant_id: membershipForTenant.tenant_id,
            status: membershipForTenant.status,
            tenants: membershipForTenant.tenant, // Set the current tenant
            all_tenants: member.all_tenants,
            all_memberships: member.all_memberships,
          };
          setActiveMember(modifiedMember);
          return;
        }
      }

      // If no tenant specified or tenant not found, use the default member
      setActiveMember(member);
    }
  }, [member, searchParams]);

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user || !activeMember) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-lg">Loading member data...</p>
        </div>
      </div>
    );
  }

  return <MemberDashboardClient member={activeMember} />;
}
