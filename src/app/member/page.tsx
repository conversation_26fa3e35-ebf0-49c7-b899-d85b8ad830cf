"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";

export default function MemberPortalPage() {
  return (
    <div className="w-full min-h-screen flex-col bg-gray-50">
      <header className="sticky top-0 z-40 border-b bg-background">
        <div className="container mx-auto flex h-16 items-center justify-between py-4">
          <div className="flex items-center gap-2">
            <span className="text-xl font-bold">GymSaaS</span>
          </div>
          <div className="flex items-center gap-4">
            <Button variant="ghost" asChild>
              <Link href="/">Home</Link>
            </Button>
          </div>
        </div>
      </header>
      <main className="w-full">
        <section className="py-20 md:py-32">
          <div className="container mx-auto flex flex-col items-center text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">
              Member Portal
            </h1>
            <p className="mt-6 max-w-3xl text-lg text-muted-foreground sm:text-xl">
              Access your membership information, view subscriptions, and manage
              your account.
            </p>
            <div className="mt-10 flex flex-col gap-4 sm:flex-row">
              <Button size="lg" asChild>
                <Link href="/member/login">Login to Portal</Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="/member/register">Create Account</Link>
              </Button>
              <Button size="lg" variant="ghost" asChild>
                <Link href="/member/reset-password">Reset Password</Link>
              </Button>
            </div>
          </div>
        </section>

        <section className="py-20 bg-muted/50">
          <div className="container mx-auto">
            <div className="mb-12 text-center">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
                Member Benefits
              </h2>
              <p className="mt-4 text-lg text-muted-foreground">
                Everything you can do in your member portal
              </p>
            </div>
            <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
              <div className="rounded-lg border bg-card p-6 shadow-sm">
                <h3 className="text-xl font-bold">View Subscriptions</h3>
                <p className="mt-2 text-muted-foreground">
                  See your current and past subscription plans.
                </p>
              </div>
              <div className="rounded-lg border bg-card p-6 shadow-sm">
                <h3 className="text-xl font-bold">Payment History</h3>
                <p className="mt-2 text-muted-foreground">
                  Track all your payments in one place.
                </p>
              </div>
              <div className="rounded-lg border bg-card p-6 shadow-sm">
                <h3 className="text-xl font-bold">Manage Profile</h3>
                <p className="mt-2 text-muted-foreground">
                  Update your personal information and password.
                </p>
              </div>
            </div>
          </div>
        </section>
      </main>
      <footer className="border-t py-6 md:py-10">
        <div className="container mx-auto flex flex-col items-center justify-between gap-4 md:flex-row">
          <p className="text-sm text-muted-foreground">
            &copy; {new Date().getFullYear()} GymSaaS. All rights reserved.
          </p>
          <div className="flex gap-4">
            <Link
              href="#"
              className="text-sm text-muted-foreground hover:underline"
            >
              Terms
            </Link>
            <Link
              href="#"
              className="text-sm text-muted-foreground hover:underline"
            >
              Privacy
            </Link>
            <Link
              href="#"
              className="text-sm text-muted-foreground hover:underline"
            >
              Contact
            </Link>
          </div>
        </div>
      </footer>
    </div>
  );
}
