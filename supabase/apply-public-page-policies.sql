-- Add public page fields to tenants table if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tenants' AND column_name = 'public_page_enabled') THEN
        ALTER TABLE tenants ADD COLUMN public_page_enabled BOOLEAN NOT NULL DEFAULT FALSE;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tenants' AND column_name = 'public_page_title') THEN
        ALTER TABLE tenants ADD COLUMN public_page_title TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tenants' AND column_name = 'public_page_description') THEN
        ALTER TABLE tenants ADD COLUMN public_page_description TEXT;
    END IF;
END
$$;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS tenant_public_select_policy ON tenants;
DROP POLICY IF EXISTS subscription_plans_public_select_policy ON subscription_plans;

-- Create a policy to allow public access to tenant information for tenants with public_page_enabled=true
CREATE POLICY tenant_public_select_policy ON tenants
  FOR SELECT
  USING (
    public_page_enabled = TRUE
  );

-- Create a policy to allow public access to subscription plans for tenants with public_page_enabled=true
CREATE POLICY subscription_plans_public_select_policy ON subscription_plans
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM tenants
      WHERE tenants.id = subscription_plans.tenant_id
      AND tenants.public_page_enabled = TRUE
      AND subscription_plans.is_active = TRUE
    )
  );

-- Make sure RLS is enabled on these tables
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_plans ENABLE ROW LEVEL SECURITY;

-- Update a test tenant to have public_page_enabled=true for testing
UPDATE tenants
SET 
  public_page_enabled = TRUE,
  public_page_title = 'Membership Plans',
  public_page_description = 'Choose the perfect membership plan for you'
WHERE slug = 'gym-anooj';
