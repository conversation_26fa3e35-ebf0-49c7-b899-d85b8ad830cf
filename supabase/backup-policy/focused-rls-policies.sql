-- Focused RLS policies for core tables only
-- This script creates RLS policies for tenants, tenant_users, and members
-- and disables RLS for other tables to simplify the implementation

-- First, enable RLS on core tables
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE tenant_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE members ENABLE ROW LEVEL SECURITY;

-- Disable RLS on other tables for simplicity
ALTER TABLE subscription_plans DISABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions DISABLE ROW LEVEL SECURITY;
ALTER TABLE payments DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies
DROP POLICY IF EXISTS tenant_select_policy ON tenants;
DROP POLICY IF EXISTS tenant_insert_policy ON tenants;
DROP POLICY IF EXISTS tenant_update_policy ON tenants;
DROP POLICY IF EXISTS tenant_delete_policy ON tenants;

DROP POLICY IF EXISTS tenant_users_select_policy ON tenant_users;
DROP POLICY IF EXISTS tenant_users_insert_policy ON tenant_users;
DROP POLICY IF EXISTS tenant_users_update_policy ON tenant_users;
DROP POLICY IF EXISTS tenant_users_delete_policy ON tenant_users;

DROP POLICY IF EXISTS members_select_policy ON members;
DROP POLICY IF EXISTS members_insert_policy ON members;
DROP POLICY IF EXISTS members_update_policy ON members;
DROP POLICY IF EXISTS members_delete_policy ON members;

-- Create helper functions to avoid recursion
-- Create a function to check if a user is a member of a tenant
CREATE OR REPLACE FUNCTION is_tenant_member(tenant_id_arg UUID, user_id_arg UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM tenant_users
    WHERE tenant_id = tenant_id_arg AND user_id = user_id_arg
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to check if a user is an admin of a tenant
CREATE OR REPLACE FUNCTION is_tenant_admin(tenant_id_arg UUID, user_id_arg UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM tenant_users
    WHERE tenant_id = tenant_id_arg AND user_id = user_id_arg AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 1. Tenant Policies
-- These are the foundation of the multi-tenant system

-- Tenant SELECT policy: Users can see tenants they own or are members of
CREATE POLICY tenant_select_policy ON tenants
  FOR SELECT
  USING (
    owner_id = auth.uid() OR is_tenant_member(id, auth.uid())
  );

-- Tenant INSERT policy: Only the owner can create a tenant
CREATE POLICY tenant_insert_policy ON tenants
  FOR INSERT
  WITH CHECK (
    owner_id = auth.uid()
  );

-- Tenant UPDATE policy: Owner or admin can update tenant
CREATE POLICY tenant_update_policy ON tenants
  FOR UPDATE
  USING (
    owner_id = auth.uid() OR is_tenant_admin(id, auth.uid())
  );

-- Tenant DELETE policy: Only the owner can delete a tenant
CREATE POLICY tenant_delete_policy ON tenants
  FOR DELETE
  USING (
    owner_id = auth.uid()
  );

-- 2. Tenant Users Policies
-- These control who can see and manage tenant members

-- Create a function to check if a user is the owner of a tenant
CREATE OR REPLACE FUNCTION is_tenant_owner(tenant_id_arg UUID, user_id_arg UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM tenants
    WHERE id = tenant_id_arg AND owner_id = user_id_arg
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Tenant Users SELECT policy: Users can see their own records or records for tenants they own
CREATE POLICY tenant_users_select_policy ON tenant_users
  FOR SELECT
  USING (
    -- User can see their own tenant_users records
    user_id = auth.uid()
    OR
    -- User can see tenant_users records if they are the owner of the tenant
    is_tenant_owner(tenant_id, auth.uid())
    OR
    -- User can see tenant_users records if they are an admin of the tenant
    is_tenant_admin(tenant_id, auth.uid())
  );

-- Tenant Users INSERT policy: Owner or admin can add users to tenant
CREATE POLICY tenant_users_insert_policy ON tenant_users
  FOR INSERT
  WITH CHECK (
    -- User can add tenant_users if they are the owner of the tenant
    is_tenant_owner(tenant_id, auth.uid())
    OR
    -- User can add tenant_users if they are an admin of the tenant
    is_tenant_admin(tenant_id, auth.uid())
  );

-- Tenant Users UPDATE policy: Owner or admin can update tenant users
CREATE POLICY tenant_users_update_policy ON tenant_users
  FOR UPDATE
  USING (
    -- User can update tenant_users if they are the owner of the tenant
    is_tenant_owner(tenant_id, auth.uid())
    OR
    -- User can update tenant_users if they are an admin of the tenant
    is_tenant_admin(tenant_id, auth.uid())
  );

-- Tenant Users DELETE policy: Owner or admin can remove users from tenant
CREATE POLICY tenant_users_delete_policy ON tenant_users
  FOR DELETE
  USING (
    -- User can delete tenant_users if they are the owner of the tenant
    is_tenant_owner(tenant_id, auth.uid())
    OR
    -- User can delete tenant_users if they are an admin of the tenant
    is_tenant_admin(tenant_id, auth.uid())
  );

-- 3. Members Policies
-- These control access to gym members data

-- Members SELECT policy: Users can see members of tenants they belong to
CREATE POLICY members_select_policy ON members
  FOR SELECT
  USING (
    -- User can see members if they are the owner of the tenant
    is_tenant_owner(tenant_id, auth.uid())
    OR
    -- User can see members if they are a user of the tenant
    is_tenant_member(tenant_id, auth.uid())
  );

-- Members INSERT policy: Users can add members to tenants they belong to
CREATE POLICY members_insert_policy ON members
  FOR INSERT
  WITH CHECK (
    -- User can add members if they are the owner of the tenant
    is_tenant_owner(tenant_id, auth.uid())
    OR
    -- User can add members if they are a user of the tenant
    is_tenant_member(tenant_id, auth.uid())
  );

-- Members UPDATE policy: Users can update members of tenants they belong to
CREATE POLICY members_update_policy ON members
  FOR UPDATE
  USING (
    -- User can update members if they are the owner of the tenant
    is_tenant_owner(tenant_id, auth.uid())
    OR
    -- User can update members if they are a user of the tenant
    is_tenant_member(tenant_id, auth.uid())
  );

-- Members DELETE policy: Only owner or admin can delete members
CREATE POLICY members_delete_policy ON members
  FOR DELETE
  USING (
    -- User can delete members if they are the owner of the tenant
    is_tenant_owner(tenant_id, auth.uid())
    OR
    -- User can delete members if they are an admin of the tenant
    is_tenant_admin(tenant_id, auth.uid())
  );
