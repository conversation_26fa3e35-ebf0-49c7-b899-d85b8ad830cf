-- Add auth-related fields to members table
ALTER TABLE members ADD COLUMN IF NOT EXISTS auth_id UUID REFERENCES auth.users(id) ON DELETE SET NULL;
ALTER TABLE members ADD COLUMN IF NOT EXISTS password_set B<PERSON><PERSON>EAN NOT NULL DEFAULT FALSE;

-- Create a policy to allow members to access their own data
CREATE POLICY member_self_select_policy ON members
  FOR SELECT
  USING (
    auth_id = auth.uid()
  );

-- Create a policy to allow members to update their own data
CREATE POLICY member_self_update_policy ON members
  FOR UPDATE
  USING (
    auth_id = auth.uid()
  );

-- Create a policy to allow members to view their own subscriptions
CREATE POLICY member_subscriptions_select_policy ON subscriptions
  FOR SELECT
  USING (
    member_id IN (
      SELECT id FROM members WHERE auth_id = auth.uid()
    )
  );

-- Create a policy to allow members to view their own payments
CREATE POLICY member_payments_select_policy ON payments
  FOR SELECT
  USING (
    subscription_id IN (
      SELECT id FROM subscriptions WHERE member_id IN (
        SELECT id FROM members WHERE auth_id = auth.uid()
      )
    )
  );

-- Create a policy to allow members to view subscription plans related to their subscriptions
CREATE POLICY member_subscription_plans_select_policy ON subscription_plans
  FOR SELECT
  USING (
    id IN (
      SELECT plan_id FROM subscriptions WHERE member_id IN (
        SELECT id FROM members WHERE auth_id = auth.uid()
      )
    )
  );

-- Create a policy to allow members to view their tenant
CREATE POLICY member_tenant_select_policy ON tenants
  FOR SELECT
  USING (
    id IN (
      SELECT tenant_id FROM members WHERE auth_id = auth.uid()
    )
  );
