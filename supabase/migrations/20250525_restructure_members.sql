-- Migration to restructure members for global uniqueness
-- This migration:
-- 1. Creates a new global members table
-- 2. Creates a new tenant_members junction table
-- 3. Migrates existing data
-- 4. Updates RLS policies

-- Step 1: Create temporary tables to store existing data
CREATE TABLE temp_members AS
SELECT * FROM members;

-- Step 2: Drop existing constraints and indexes related to members
ALTER TABLE subscriptions DROP CONSTRAINT IF EXISTS subscriptions_member_id_fkey;
DROP INDEX IF EXISTS idx_subscriptions_member_id;
DROP INDEX IF EXISTS idx_members_tenant_id;
DROP INDEX IF EXISTS idx_members_email;

-- Step 3: Drop existing members table
DROP TABLE members;

-- Step 4: Create new global members table
CREATE TABLE members (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  email TEXT NOT NULL UNIQUE,
  phone TEXT UNIQUE,
  auth_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  password_set BOOLEAN NOT NULL DEFAULT FALSE
);

-- Step 5: Create new tenant_members junction table
CREATE TABLE tenant_members (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  member_id UUID NOT NULL REFERENCES members(id) ON DELETE CASCADE,
  status TEXT NOT NULL CHECK (status IN ('active', 'inactive', 'pending')),
  notes TEXT,
  UNIQUE(tenant_id, member_id)
);

-- Step 6: Migrate existing data
-- First, insert unique members into the new members table
INSERT INTO members (id, created_at, first_name, last_name, email, phone, auth_id, password_set)
SELECT 
  id, 
  created_at, 
  first_name, 
  last_name, 
  email, 
  phone, 
  auth_id, 
  COALESCE(password_set, FALSE)
FROM (
  SELECT DISTINCT ON (email) 
    id, 
    created_at, 
    first_name, 
    last_name, 
    email, 
    phone, 
    auth_id, 
    password_set
  FROM temp_members
  ORDER BY email, created_at
) AS unique_members;

-- Then, create tenant_members associations
INSERT INTO tenant_members (tenant_id, member_id, status, notes, created_at)
SELECT 
  tm.tenant_id, 
  m.id, 
  tm.status, 
  tm.notes, 
  tm.created_at
FROM temp_members tm
JOIN members m ON tm.email = m.email;

-- Step 7: Update subscriptions to reference the correct member_id
-- First, create a temporary mapping table
CREATE TABLE temp_member_mapping AS
SELECT 
  tm.id AS old_id, 
  m.id AS new_id
FROM temp_members tm
JOIN members m ON tm.email = m.email;

-- Then update subscriptions
ALTER TABLE subscriptions
ADD CONSTRAINT subscriptions_member_id_fkey
FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE;

-- Step 8: Create new indexes
CREATE INDEX idx_tenant_members_tenant_id ON tenant_members(tenant_id);
CREATE INDEX idx_tenant_members_member_id ON tenant_members(member_id);
CREATE INDEX idx_members_email ON members(email);
CREATE INDEX idx_members_phone ON members(phone);
CREATE INDEX idx_members_auth_id ON members(auth_id);
CREATE INDEX idx_subscriptions_member_id ON subscriptions(member_id);

-- Step 9: Create or update RLS policies
-- Enable RLS on the new tables
ALTER TABLE members ENABLE ROW LEVEL SECURITY;
ALTER TABLE tenant_members ENABLE ROW LEVEL SECURITY;

-- Drop existing policies for members
DROP POLICY IF EXISTS members_select_policy ON members;
DROP POLICY IF EXISTS members_insert_policy ON members;
DROP POLICY IF EXISTS members_update_policy ON members;
DROP POLICY IF EXISTS members_delete_policy ON members;
DROP POLICY IF EXISTS member_self_select_policy ON members;
DROP POLICY IF EXISTS member_self_update_policy ON members;

-- Create a function to check if a user is associated with a member
CREATE OR REPLACE FUNCTION is_member_in_tenant(member_id_arg UUID, user_id_arg UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM tenant_members tm
    JOIN tenants t ON tm.tenant_id = t.id
    JOIN tenant_users tu ON t.id = tu.tenant_id
    WHERE tm.member_id = member_id_arg 
    AND tu.user_id = user_id_arg
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to check if a member belongs to a tenant
CREATE OR REPLACE FUNCTION is_member_of_tenant(member_id_arg UUID, tenant_id_arg UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM tenant_members
    WHERE member_id = member_id_arg AND tenant_id = tenant_id_arg
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Members SELECT policy: Users can see members they have access to through tenants
CREATE POLICY members_select_policy ON members
  FOR SELECT
  USING (
    -- User can see members if they are associated with any tenant the user belongs to
    EXISTS (
      SELECT 1
      FROM tenant_members tm
      JOIN tenant_users tu ON tm.tenant_id = tu.tenant_id
      WHERE tm.member_id = members.id AND tu.user_id = auth.uid()
    )
    OR
    -- Members can see their own data
    auth_id = auth.uid()
  );

-- Members INSERT policy: Anyone can create a member (will be restricted at application level)
CREATE POLICY members_insert_policy ON members
  FOR INSERT
  WITH CHECK (true);

-- Members UPDATE policy: Users can update members they have access to
CREATE POLICY members_update_policy ON members
  FOR UPDATE
  USING (
    -- User can update members if they are associated with any tenant the user belongs to
    EXISTS (
      SELECT 1
      FROM tenant_members tm
      JOIN tenant_users tu ON tm.tenant_id = tu.tenant_id
      WHERE tm.member_id = members.id AND tu.user_id = auth.uid()
    )
    OR
    -- Members can update their own data
    auth_id = auth.uid()
  );

-- Members DELETE policy: Only admin users can delete members
CREATE POLICY members_delete_policy ON members
  FOR DELETE
  USING (
    -- User can delete members if they are an admin of any tenant the member belongs to
    EXISTS (
      SELECT 1
      FROM tenant_members tm
      JOIN tenant_users tu ON tm.tenant_id = tu.tenant_id
      WHERE tm.member_id = members.id AND tu.user_id = auth.uid() AND tu.role = 'admin'
    )
  );

-- Tenant Members SELECT policy: Users can see tenant_members for tenants they belong to
CREATE POLICY tenant_members_select_policy ON tenant_members
  FOR SELECT
  USING (
    -- User can see tenant_members if they are a user of the tenant
    is_tenant_member(tenant_id, auth.uid())
    OR
    -- User can see tenant_members if they are the owner of the tenant
    is_tenant_owner(tenant_id, auth.uid())
  );

-- Tenant Members INSERT policy: Users can add tenant_members to tenants they belong to
CREATE POLICY tenant_members_insert_policy ON tenant_members
  FOR INSERT
  WITH CHECK (
    -- User can add tenant_members if they are a user of the tenant
    is_tenant_member(tenant_id, auth.uid())
    OR
    -- User can add tenant_members if they are the owner of the tenant
    is_tenant_owner(tenant_id, auth.uid())
  );

-- Tenant Members UPDATE policy: Users can update tenant_members for tenants they belong to
CREATE POLICY tenant_members_update_policy ON tenant_members
  FOR UPDATE
  USING (
    -- User can update tenant_members if they are a user of the tenant
    is_tenant_member(tenant_id, auth.uid())
    OR
    -- User can update tenant_members if they are the owner of the tenant
    is_tenant_owner(tenant_id, auth.uid())
  );

-- Tenant Members DELETE policy: Only owner or admin can delete tenant_members
CREATE POLICY tenant_members_delete_policy ON tenant_members
  FOR DELETE
  USING (
    -- User can delete tenant_members if they are the owner of the tenant
    is_tenant_owner(tenant_id, auth.uid())
    OR
    -- User can delete tenant_members if they are an admin of the tenant
    is_tenant_admin(tenant_id, auth.uid())
  );

-- Step 10: Create a function to find or create a member
CREATE OR REPLACE FUNCTION find_or_create_member(
  p_first_name TEXT,
  p_last_name TEXT,
  p_email TEXT,
  p_phone TEXT
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_member_id UUID;
BEGIN
  -- Try to find the member by email
  SELECT id INTO v_member_id FROM members WHERE email = p_email;
  
  -- If not found, try to find by phone (if provided)
  IF v_member_id IS NULL AND p_phone IS NOT NULL THEN
    SELECT id INTO v_member_id FROM members WHERE phone = p_phone;
  END IF;
  
  -- If still not found, create a new member
  IF v_member_id IS NULL THEN
    INSERT INTO members (first_name, last_name, email, phone)
    VALUES (p_first_name, p_last_name, p_email, p_phone)
    RETURNING id INTO v_member_id;
  END IF;
  
  RETURN v_member_id;
END;
$$;

-- Step 11: Create a function to add a member to a tenant
CREATE OR REPLACE FUNCTION add_member_to_tenant(
  p_member_id UUID,
  p_tenant_id UUID,
  p_status TEXT DEFAULT 'active',
  p_notes TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_tenant_member_id UUID;
BEGIN
  -- Check if the member is already in the tenant
  SELECT id INTO v_tenant_member_id 
  FROM tenant_members 
  WHERE member_id = p_member_id AND tenant_id = p_tenant_id;
  
  -- If not found, add the member to the tenant
  IF v_tenant_member_id IS NULL THEN
    INSERT INTO tenant_members (member_id, tenant_id, status, notes)
    VALUES (p_member_id, p_tenant_id, p_status, p_notes)
    RETURNING id INTO v_tenant_member_id;
  END IF;
  
  RETURN v_tenant_member_id;
END;
$$;

-- Step 12: Clean up temporary tables
DROP TABLE temp_members;
DROP TABLE temp_member_mapping;
