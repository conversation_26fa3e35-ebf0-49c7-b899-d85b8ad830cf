-- Add public page fields to tenants table
ALTER TABLE tenants ADD COLUMN public_page_enabled BOOLEAN NOT NULL DEFAULT FALSE;
ALTER TABLE tenants ADD COLUMN public_page_title TEXT;
ALTER TABLE tenants ADD COLUMN public_page_description TEXT;

-- Create a policy to allow public access to tenant information for tenants with public_page_enabled=true
CREATE POLICY tenant_public_select_policy ON tenants
  FOR SELECT
  USING (
    public_page_enabled = TRUE
  );

-- Create a policy to allow public access to subscription plans for tenants with public_page_enabled=true
CREATE POLICY subscription_plans_public_select_policy ON subscription_plans
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM tenants
      WHERE tenants.id = subscription_plans.tenant_id
      AND tenants.public_page_enabled = TRUE
      AND subscription_plans.is_active = TRUE
    )
  );
