-- Create a function to create a member auth account
CREATE OR REPLACE FUNCTION create_member_auth(
  p_email TEXT,
  p_password TEXT,
  p_member_id UUID
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_user_id UUID;
  v_result JSONB;
BEGIN
  -- Create the auth user
  v_user_id := extensions.uuid_generate_v4();
  
  INSERT INTO auth.users (
    id,
    email,
    email_confirmed_at,
    encrypted_password,
    raw_app_meta_data,
    raw_user_meta_data,
    created_at,
    updated_at,
    confirmation_token,
    email_change_token_new,
    recovery_token
  )
  VALUES (
    v_user_id,
    p_email,
    now(),
    crypt(p_password, gen_salt('bf')),
    '{"provider":"email","providers":["email"]}',
    '{}',
    now(),
    now(),
    '',
    '',
    ''
  );
  
  -- Add the user to the authenticated role
  INSERT INTO auth.identities (
    id,
    user_id,
    identity_data,
    provider,
    last_sign_in_at,
    created_at,
    updated_at
  )
  VALUES (
    v_user_id,
    v_user_id,
    jsonb_build_object('sub', v_user_id, 'email', p_email),
    'email',
    now(),
    now(),
    now()
  );
  
  -- Update the member with the auth_id
  UPDATE members
  SET 
    auth_id = v_user_id,
    password_set = TRUE
  WHERE id = p_member_id;
  
  -- Return the result
  v_result := jsonb_build_object(
    'user_id', v_user_id,
    'member_id', p_member_id
  );
  
  RETURN v_result;
END;
$$;
